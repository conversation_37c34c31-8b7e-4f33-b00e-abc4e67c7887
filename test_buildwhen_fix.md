# Test Plan for Fixed buildWhen Logic in TabManager

## Test Scenario: Orange Indicator Shows/Hides Correctly with Proper buildWhen Logic

### Prerequisites
1. Have at least one tab open in the tab manager
2. The tab should have some request data (URL, headers, body, etc.)
3. Be able to make modifications to the request data

### Test Steps

#### Test Case 1: Orange Indicator Appears When Tab is Modified
1. **Open a tab with request data**
   - Open any request tab
   - Verify the tab has no orange indicator initially
   - Expected: No orange dot visible, `isModified = false`

2. **Make changes to the request**
   - Modify URL field (e.g., add "/test" to the end)
   - Expected: Orange dot appears immediately next to the tab name
   - Expected: Debug logs show "TabManager: Rebuilding due to isModified change for tab [uuid]: false -> true"

3. **Verify indicator is visible**
   - Check that the orange dot (6x6 pixels) is visible next to the tab name
   - Expected: Orange indicator is clearly visible
   - Expected: Tab shows `isModified = true`

#### Test Case 2: Orange Indicator Disappears When Changes are Reverted
1. **Start with a modified tab**
   - Have a tab with orange indicator showing
   - Verify `isModified = true`

2. **Revert changes back to original**
   - Change the URL back to its original value
   - Expected: Orange dot disappears immediately
   - Expected: Debug logs show "TabManager: Rebuilding due to isModified change for tab [uuid]: true -> false"

3. **Verify indicator is hidden**
   - Check that no orange dot is visible
   - Expected: No orange indicator visible
   - Expected: Tab shows `isModified = false`

#### Test Case 3: Multiple Tabs with Different Modification States
1. **Open multiple tabs**
   - Open 3-4 different tabs
   - Modify some tabs but not others

2. **Verify individual tab indicators**
   - Expected: Only modified tabs show orange indicators
   - Expected: Unmodified tabs show no indicators
   - Expected: Each tab maintains its own modification state

3. **Switch between tabs**
   - Click on different tabs to switch between them
   - Expected: Orange indicators persist correctly when switching
   - Expected: Debug logs show "TabManager: Rebuilding due to active tab change"

#### Test Case 4: Real-time Updates During Typing
1. **Type in URL field**
   - Start typing in the URL field
   - Expected: Orange indicator appears as soon as the first character is typed
   - Expected: Indicator remains visible while typing

2. **Delete all changes**
   - Delete all typed characters to return to original URL
   - Expected: Orange indicator disappears when URL returns to original
   - Expected: Real-time updates work smoothly

#### Test Case 5: Response Loading Does Not Affect Indicator
1. **Send a request**
   - Send a request and receive response
   - Expected: No orange indicator appears due to response loading
   - Expected: Debug logs should NOT show modification changes due to response

2. **Modify request after response**
   - Make changes to the request after response is loaded
   - Expected: Orange indicator appears for actual modifications
   - Expected: Response data does not interfere with modification tracking

### Expected Debug Logs

The following debug logs should appear when the buildWhen logic is working correctly:

```
// When tab count changes
TabManager: Rebuilding due to tab count change: 1 -> 2

// When active tab changes
TabManager: Rebuilding due to active tab change: [uuid1] -> [uuid2]

// When active tab properties change
TabManager: Rebuilding due to active tab property change for tab [uuid]
  - URL: https://api.example.com -> https://api.example.com/test
  - Method: GET -> GET
  - TabName: Request 1 -> Request 1
  - IsModified: false -> true

// When any tab's isModified status changes
TabManager: Rebuilding due to isModified change for tab [uuid]: false -> true
TabManager: Rebuilding due to isModified change for tab [uuid]: true -> false

// When tab display properties change
TabManager: Rebuilding due to tab display property change for tab [uuid]
```

### buildWhen Logic Verification

The fixed `buildWhen` logic now properly handles:

1. **Tab Count Changes**: Rebuilds when tabs are added/removed
2. **Active Tab Changes**: Rebuilds when switching between tabs
3. **Active Tab Property Changes**: Rebuilds when active tab's URL, method, name, or isModified changes
4. **Any Tab Modification Changes**: Rebuilds when ANY tab's isModified status changes
5. **Tab Display Property Changes**: Rebuilds when tab name or method changes for display

### What Should Happen

✅ **Immediate Updates**: Orange indicator appears/disappears immediately when modifications are made/reverted
✅ **Accurate State**: `isModified` value correctly reflects the actual modification state
✅ **Multiple Tab Support**: Each tab maintains its own modification state independently
✅ **Performance**: Only rebuilds when necessary, not on every state change
✅ **Debug Visibility**: Clear debug logs show exactly why rebuilds are happening

### What Should NOT Happen

❌ Widget not rebuilding when `isModified` changes
❌ Orange indicator stuck in wrong state
❌ Indicator not appearing when modifications are made
❌ Indicator not disappearing when changes are reverted
❌ Excessive rebuilds causing performance issues

### Implementation Details

The fixed `buildWhen` logic:

1. **Checks tab count changes** for add/remove operations
2. **Checks active tab changes** for tab switching
3. **Checks active tab properties** for URL, method, name, isModified changes
4. **Iterates through all tabs** to detect isModified changes for any tab
5. **Checks display properties** for tab name and method changes
6. **Provides detailed debug logging** for troubleshooting

### Performance Considerations

- Only rebuilds when necessary state changes occur
- Efficient iteration through tabs to check modification status
- Detailed logging helps identify unnecessary rebuilds
- Proper state comparison prevents false positives

This fix ensures that the orange modification indicator works perfectly with accurate real-time updates and proper state management.
