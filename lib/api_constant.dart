class ApiConstant {
  static String baseUrl = "https://dev.kuickapi.dinetestapi.com/api/";

  static String cmsWorkspaceId = "1";
  static var collectionID = 0;
  static var folderID = 0;
  static var requestID = 0;
  static var userID = 1;


  static String login = "${baseUrl}login";
  static String register = "${baseUrl}register";

  static String getCollectionsList = "${baseUrl}collection/list";
  static String getCollectionDetails = "${baseUrl}collection/";
  static String createCollection = "${baseUrl}collection/create";
  static String updateCollection = "${baseUrl}collection/update/";
  static String deleteCollection = "${baseUrl}collection/delete";

  static String getFolderDetails = "${baseUrl}folder/";
  static String createFolder = "${baseUrl}folder/create";
  static String updateFolder = "${baseUrl}folder/update/";
  static String deleteFolder = "${baseUrl}folder/delete";

  static String createRequest = "${baseUrl}request/create";
  static String updateRequest = "${baseUrl}request/update/";
  static String deleteRequest = "${baseUrl}request/delete";
  static String getRequestDetails = "${baseUrl}request/";

  static String sendRequest = "${baseUrl}send/direct";
  static String saveRequest = "${baseUrl}request/save/";


  static String getHistory = "${baseUrl}history/list";
  static String clearHistory = "${baseUrl}history/clear";

}

class ApiHeaders{
  static Map<String, String> headers = {
    'Content-Type': 'application/json',
  };
}

class CommonConstants{
  static var collectionID = 0;
}