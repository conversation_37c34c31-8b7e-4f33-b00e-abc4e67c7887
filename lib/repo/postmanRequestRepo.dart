import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:postman_flutter/api_constant.dart';
import 'package:postman_flutter/model/request_model.dart';

class PostmanRequestResponse {
  final bool success;
  final String message;
  final RequestModel? data;

  PostmanRequestResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory PostmanRequestResponse.fromJson(Map<String, dynamic> json) {
    return PostmanRequestResponse(
      success: json['status'] == 'success',
      message: json['msg'] ?? '',
      data: json['data'] != null ? RequestModel.fromJson(json['data']) : null,
    );
  }

  factory PostmanRequestResponse.error(String message) {
    return PostmanRequestResponse(
      success: false,
      message: message,
    );
  }
}

class PostmanRequestRepository {
  Future<PostmanRequestResponse> createRequest({
    required String name,
    required String url,
    required String method,
    String? collectionId,
    String? folderId,
    required int workspaceId,
  }) async {
    final apiUrl = Uri.parse(ApiConstant.createRequest);
    
    // Build request parameters based on whether it's a collection or folder request
    final params = {
      'name': name,
      'url': url,
      'method': method,
      'workspace_id': workspaceId.toString(),
    };
    
    // Add either collection_id or folder_id based on what's provided
    if (collectionId != null) {
      params['collection_id'] = collectionId;
    } else if (folderId != null) {
      params['folder_id'] = folderId;
    }
    
    try {
      debugPrint('Creating request with params: $params');
      final response = await http.post(
        apiUrl,
        body: params,
      );
      
      debugPrint('Create request response: ${response.body}');
      
      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return PostmanRequestResponse.fromJson(jsonResponse);
      } else {
        return PostmanRequestResponse.error('Failed to create request. Status code: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error creating request: $e');
      return PostmanRequestResponse.error('Error creating request: $e');
    }
  }
}
