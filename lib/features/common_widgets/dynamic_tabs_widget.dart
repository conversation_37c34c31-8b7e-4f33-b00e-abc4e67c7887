import 'package:flutter/material.dart';
import 'package:postman_flutter/helpers/color_config.dart';

/*class DynamicTabsWidget extends StatelessWidget {
  final List<String> tabTitles;
  final List<Widget> tabViews;

  const DynamicTabsWidget({
    Key? key,
    required this.tabTitles,
    required this.tabViews,
  })  : assert(tabTitles.length == tabViews.length,
  'tabTitles and tabViews must have the same length'),
        super(key: key);

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: tabTitles.length,
      child: Column(
        children: [
          Container(
            color: AppThemeColor.commonBackground,
            child: TabBar(
              isScrollable: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: EdgeInsets.zero,
              labelColor: Colors.white,
              //indicatorColor: Colors.blue,
              indicatorPadding: const EdgeInsets.only(top: 33, bottom: 10),
              indicatorWeight: 1,
              indicator: UnderlineTabIndicator(
                  borderSide: BorderSide(
                      width: 2.0,
                      color: AppThemeColor.tabbarUnderLineColor,
                  ),

              ),
              dividerColor: Colors.transparent,
              tabAlignment: TabAlignment.start,
              labelPadding: EdgeInsets.only(right: 20),
              unselectedLabelColor: AppThemeColor.tabUnselectedTextColor,
              tabs: tabTitles
                  .map((title) => Tab(text: title))
                  .toList(),
            ),
          ),
          Expanded(
            child: TabBarView(
              children: tabViews,
            ),
          ),
        ],
      ),
    );
  }
}*/


class DynamicTabsWidget extends StatefulWidget {
  final List<String> tabTitles;
  final List<Widget> tabViews;

  const DynamicTabsWidget({
    Key? key,
    required this.tabTitles,
    required this.tabViews,
  })  : assert(tabTitles.length == tabViews.length,
  'tabTitles and tabViews must have the same length'),
        super(key: key);

  @override
  _DynamicTabsWidgetState createState() => _DynamicTabsWidgetState();
}

class _DynamicTabsWidgetState extends State<DynamicTabsWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: widget.tabTitles.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          color: AppThemeColor.commonBackground,
          child: TabBar(
            controller: _tabController,
            isScrollable: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.zero,
            labelColor: Colors.white,
            indicatorPadding: const EdgeInsets.only(top: 33, bottom: 10),
            indicatorWeight: 1,
            indicator: UnderlineTabIndicator(
              borderSide: BorderSide(
                width: 2.0,
                color: AppThemeColor.tabbarUnderLineColor,
              ),
            ),
            dividerColor: Colors.transparent,
            tabAlignment: TabAlignment.start,
            labelPadding: const EdgeInsets.only(right: 20),
            unselectedLabelColor: AppThemeColor.tabUnselectedTextColor,
            tabs: widget.tabTitles
                .map((title) => Tab(text: title))
                .toList(),
          ),
        ),
        Expanded(
          child: AnimatedBuilder(
            animation: _tabController,
            builder: (context, _) {
              return IndexedStack(
                index: _tabController.index,
                children: widget.tabViews,
              );
            },
          ),
        ),
      ],
    );
  }
}

class VerticalDynamicTabsWidget extends StatefulWidget {
  final List<String> tabTitles;
  final List<Widget> tabViews;

  const VerticalDynamicTabsWidget({
    Key? key,
    required this.tabTitles,
    required this.tabViews,
  })  : assert(tabTitles.length == tabViews.length,
  'tabTitles and tabViews must have the same length'),
        super(key: key);

  @override
  _VerticalDynamicTabsWidgetState createState() => _VerticalDynamicTabsWidgetState();
}

class _VerticalDynamicTabsWidgetState extends State<VerticalDynamicTabsWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: widget.tabTitles.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 50, // Adjust width as needed
          color: AppThemeColor.commonBackground,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: List.generate(widget.tabTitles.length, (index) {
              return MouseRegion(
                onEnter: (_) => setState(() {}),
                onExit: (_) => setState(() {}),
                child: InkWell(
                  onTap: () {
                    _tabController.animateTo(index);
                  },
                  child: AnimatedBuilder(
                    animation: _tabController,
                    builder: (context, _) {
                      bool isSelected = _tabController.index == index;
                      return Container(
                        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 10),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? AppThemeColor.checkBoxTextColor
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          widget.tabTitles[index],
                          style: TextStyle(
                            color: isSelected
                                ? Colors.white
                                : AppThemeColor.tabUnselectedTextColor,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              );
            }),
          ),
        ),
        Expanded(
          child: AnimatedBuilder(
            animation: _tabController,
            builder: (context, _) {
              return IndexedStack(
                index: _tabController.index,
                children: widget.tabViews,
              );
            },
          ),
        ),
      ],
    );
  }
}
