part of 'collection_bloc.dart';

sealed class CollectionEvent extends Equatable {
  const CollectionEvent();

  @override
  List<Object> get props => [];
}

class FetchCollections extends CollectionEvent {
  String workspaceId;
  FetchCollections(this.workspaceId);

  @override
  List<Object> get props => [workspaceId];
}


class FetchCollectionDetail extends CollectionEvent {
  final int collectionId;
  final int workspaceId;

  const FetchCollectionDetail({
    required this.collectionId,
    required this.workspaceId,
  });

  @override
  List<Object> get props => [collectionId, workspaceId];
}

class CreateCollection extends CollectionEvent {
  final String title;
  final String description;
  final int workspaceId;

  const CreateCollection({
    required this.title,
    required this.description,
    required this.workspaceId,
  });

  @override
  List<Object> get props => [title, description, workspaceId];
}

class DeleteCollection extends CollectionEvent {
  final int collectionId;

  const DeleteCollection({
    required this.collectionId,
  });

  @override
  List<Object> get props => [collectionId];
}

class UpdateCollection extends CollectionEvent {
  final int collectionId;
  final String title;
  final String description;
  final int workspaceId;

  const UpdateCollection({
    required this.collectionId,
    required this.title,
    required this.description,
    required this.workspaceId,
  });

  @override
  List<Object> get props => [collectionId, title, description, workspaceId];
}
