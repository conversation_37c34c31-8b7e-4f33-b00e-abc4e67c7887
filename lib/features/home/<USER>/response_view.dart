
import 'dart:convert';
import 'dart:convert' as dc;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_code_editor/flutter_code_editor.dart';
import 'package:flutter_highlight/themes/a11y-dark.dart';
import 'package:flutter_highlight/themes/atom-one-light.dart';
import 'package:postman_flutter/features/home/<USER>/send_request/request_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/send_request/request_state.dart';

import '../../../helpers/color_config.dart';
import '../../../helpers/style_config.dart';
import '../../common_widgets/CommonText.dart';
import 'package:highlight/languages/json.dart' show json;

import '../bloc/home_bloc.dart';
import '../bloc/home_event.dart';
import '../bloc/home_state.dart';
import '../data/tab_cache.dart';
import 'virtualized_json_view.dart';
import 'package:re_editor/re_editor.dart';
import 'package:re_highlight/languages/json.dart' show langJson;


//https://jsonplaceholder.typicode.com/users


//tested
/*class ResponseView extends StatefulWidget {
  //final TextEditingController responseController;
  const ResponseView({super.key});

  @override
  State<ResponseView> createState() => _ResponseViewState();
}

class _ResponseViewState extends State<ResponseView> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RequestBloc, RequestState>(
      builder: (context, state) {
        if (state is RequestLoadingState) {
          return const Center(child:
          CircularProgressIndicator()
          ); // Show loading state
        } else if (state is RequestErrorState) {
          debugPrint('err: ${state.error}');
          final prettyJsonError = const JsonEncoder.withIndent('  ').convert(state.error);
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 35.0),
                    child: CommonText(
                      text: "Response",
                      textStyle: mMediumTextStyle16(
                        textSize: 14,
                        textColor: AppThemeColor.white,
                      ),
                    ),
                  ),

                ],
              ),

              //const SizedBox(height: 23),

              Flexible(
                child: CodeTheme(
                  data: CodeThemeData(styles: a11yDarkTheme),
                  child: CodeField(
                    padding: EdgeInsets.only(top: 23),
                    background: AppThemeColor.commonBackground,
                    controller: CodeController(
                      text: prettyJsonError,
                      language: json,
                    ),
                    readOnly: true,
                    expands: true,
                    wrap: true,
                    textStyle: mRegularTextStyle16(textSize: 12),
                  ),
                ),
              ),

            ],
          );
        } else if (state is RequestSuccessState) {
          // Pretty-print the JSON response
          final prettyJson = const JsonEncoder.withIndent('  ').convert(state.response);

          final CodeController _codeController = CodeController(
              text: prettyJson,
              language: json,
          );

          debugPrint("Pretty JSON Response:\n$prettyJson");

          final status = state.response.toString();

          debugPrint("status:$status");

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 35.0),
                    child: CommonText(
                      text: "Response",
                      textStyle: mMediumTextStyle16(
                        textSize: 14,
                        textColor: AppThemeColor.white,
                      ),
                    ),
                  ),

                  Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: IconButton(
                      icon: Icon(
                        Icons.search,
                        size: 20,
                        color: Colors.white,
                      ),
                      onPressed: () {
                        _codeController
                            .showSearch(); // Opens the built-in search panel
                      },
                    ),
                  ),

                ],
              ),

              //const SizedBox(height: 23),

              Flexible(
                child: CodeTheme(
                  data: CodeThemeData(styles: a11yDarkTheme),
                  child: CodeField(
                    padding: EdgeInsets.only(top: 23),
                    background: AppThemeColor.commonBackground,
                    controller: _codeController,
                    readOnly: true,
                    expands: true,
                    wrap: true,
                    textStyle: mRegularTextStyle16(textSize: 12),
                  ),
                ),
              ),

            ],
          );
        }
        return SizedBox();
      },
    );
  }
}*/


//wip
class ResponseView extends StatefulWidget {
  final String uuid; // Add uuid to identify the tab

  const ResponseView({required this.uuid, super.key});

  @override
  State<ResponseView> createState() => _ResponseViewState();
}

class _ResponseViewState extends State<ResponseView> with AutomaticKeepAliveClientMixin {
  late final TextEditingController _responseController;
  late final CodeController _codeController;
  final TabCache _tabCache = TabCache();
  bool _isInitialized = false;

  @override
  bool get wantKeepAlive => true; // Keep this widget alive when it's not visible

  @override
  void initState() {
    super.initState();
    _responseController = TextEditingController();
    _codeController = CodeController(
      text: _responseController.text,
      language: json,
    );

    // Listen for changes in the response data
    _responseController.addListener(() {
      if (_responseController.text != _codeController.text) {
        _codeController.text = _responseController.text;
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateResponseController();
  }

  void _updateResponseController() {
    final activeTab = context.read<HomeBloc>().state.activeTab;

    // Only update the controller if this is the active tab
    if (activeTab != null && activeTab.uuid == widget.uuid) {
      // Check if we have cached formatted JSON for this tab
      final cachedFormattedJson = _tabCache.getFormattedJson(widget.uuid);
      if (cachedFormattedJson != null && mounted) {
        // Use cached formatted JSON instead of reprocessing
        _responseController.text = cachedFormattedJson;
        _codeController.text = cachedFormattedJson;
        _isInitialized = true;
        return;
      }

      // Check if we have cached response data for this tab
      if (_tabCache.hasResponseCache(widget.uuid)) {
        final cachedResponse = _tabCache.getCachedResponse(widget.uuid);
        if (cachedResponse != null && mounted) {
          // Use cached data instead of reprocessing
          _responseController.text = cachedResponse;
          _codeController.text = cachedResponse;
          _isInitialized = true;
          return;
        }
      }

      final responseJsonData = activeTab.responseJsonData;

      if (responseJsonData != null) {
        // Show a temporary message while formatting
        _responseController.text = "Processing response data...";
        _codeController.text = "Processing response data...";

        // Use a more efficient approach for large data
        // Defer the heavy JSON formatting to a separate isolate or microtask
        Future.microtask(() {
          if (mounted) {
            try {
              final prettyJson = const JsonEncoder.withIndent('  ').convert(responseJsonData);

              if (mounted) {
                _responseController.text = prettyJson;
                _codeController.text = prettyJson;

                // Cache both the formatted JSON and the response
                _tabCache.cacheFormattedJson(widget.uuid, prettyJson);
                _tabCache.cacheResponse(widget.uuid, prettyJson);
                _isInitialized = true;
              }
            } catch (e) {
              if (mounted) {
                final errorMsg = "Error formatting response: ${e.toString()}";
                _responseController.text = errorMsg;
                _codeController.text = errorMsg;
              }
            }
          }
        });
      } else {
        _responseController.text = "";
        _codeController.text = "";
      }
    } else {
      _responseController.text = "No response data available.";
      _codeController.text = "No response data available.";
    }
  }

  @override
  void dispose() {
    _responseController.dispose();
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Must call super.build for AutomaticKeepAliveClientMixin

    // Use BlocBuilder with buildWhen to prevent unnecessary rebuilds
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) {
        // Only rebuild if the active tab changes or if the active tab's response data changes
        final previousActiveTab = previous.activeTab;
        final currentActiveTab = current.activeTab;

        // Skip rebuilds if nothing relevant changed
        if (previousActiveTab == currentActiveTab) {
          return false;
        }

        // Active tab changed
        if (previousActiveTab?.uuid != currentActiveTab?.uuid) {
          // Only rebuild if this tab is becoming active or inactive
          return previousActiveTab?.uuid == widget.uuid || currentActiveTab?.uuid == widget.uuid;
        }

        // Only rebuild if this is the active tab and its response data changed
        if (currentActiveTab?.uuid == widget.uuid) {
          return previousActiveTab?.responseJsonData != currentActiveTab?.responseJsonData;
        }

        return false; // Don't rebuild for other state changes
      },
      builder: (context, homeState) {
        final activeTab = homeState.activeTab;

        // If there are no open tabs, clear the response data
        if (homeState.openTabs.isEmpty) {
          _responseController.text = "No response data available.";
        }

        // Only update the response data if this is the active tab
        if (activeTab != null && activeTab.uuid == widget.uuid) {
          _updateResponseController();
        }

        return BlocBuilder<RequestBloc, RequestState>(
          builder: (context, requestState) {
            if (requestState is RequestLoadingState) {
              return const Center(child: CircularProgressIndicator());
            } else if (requestState is RequestErrorState) {
              _responseController.text =
                  const JsonEncoder.withIndent('  ').convert(requestState.error);
            } else if (requestState is RequestSuccessState) {
              // Only update the response data if it belongs to this tab
              if (requestState.tabUuid == widget.uuid) {
                // Update the responseJsonData for the active tab
                context.read<HomeBloc>().add(
                  UpdateResponseJsonDataEventTab(requestState.response),
                );

                // Force update the response controller
                Future.microtask(() {
                  if (mounted) {
                    try {
                      final prettyJson = const JsonEncoder.withIndent('  ').convert(requestState.response);
                      _responseController.text = prettyJson;

                      // Cache the formatted response
                      _tabCache.cacheResponse(widget.uuid, prettyJson);
                      _isInitialized = true;
                    } catch (e) {
                      _responseController.text = "Error formatting response: ${e.toString()}";
                    }
                  }
                });
              }
            }

            return _buildResponseView();
          },
        );
      },
    );
  }

  Widget _buildResponseView() {
    final activeTab = context.read<HomeBloc>().state.activeTab;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 35.0),
              child: CommonText(
                text: "Response",
                textStyle: mMediumTextStyle16(
                  textSize: 14,
                  textColor: AppThemeColor.white,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: IconButton(
                icon: const Icon(
                  Icons.search,
                  size: 20,
                  color: Colors.white,
                ),
                onPressed: () {
                  _codeController.showSearch(); // Opens the built-in search panel
                },
              ),
            ),
          ],
        ),

        Expanded(
          child: activeTab?.responseJsonData != null
            // Use the virtualized JSON viewer for better performance with large data
            ? VirtualizedJsonView(
                jsonData: activeTab?.responseJsonData,
                isDarkMode: true,
              )
            // Fallback to the original view if no JSON data is available
            : CodeTheme(
                data: CodeThemeData(styles: a11yDarkTheme),
                child: CodeField(
                  padding: const EdgeInsets.only(top: 23),
                  background: AppThemeColor.commonBackground,
                  controller: _codeController,
                  readOnly: true,
                  expands: true,
                  wrap: true,
                  textStyle: mRegularTextStyle16(textSize: 12),
                ),
              ),
        ),
      ],
    );
  }
}

//trying with re-editor
/*class ResponseView extends StatefulWidget {
  final String uuid;

  const ResponseView({required this.uuid, super.key});

  @override
  State<ResponseView> createState() => _ResponseViewState();
}

class _ResponseViewState extends State<ResponseView> {
  late final CodeLineEditingController _codeController;
  late final CodeFindController _findController;

  @override
  void initState() {
    super.initState();
    _codeController = CodeLineEditingController.fromText("No response data available.");
    _findController = CodeFindController(_codeController);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateResponseController();
  }

  void _updateResponseController() {
    final activeTab = context.read<HomeBloc>().state.activeTab;

    if (activeTab != null && activeTab.uuid == widget.uuid) {
      final responseJsonData = activeTab.responseJsonData;
      _codeController.text = responseJsonData != null
          ? const JsonEncoder.withIndent('  ').convert(responseJsonData)
          : "No response data available.";
    } else {
      _codeController.text = "No response data available.";
    }
  }

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, homeState) {
        final activeTab = homeState.activeTab;

        if (homeState.openTabs.isEmpty) {
          _codeController.text = "No response data available.";
        }

        if (activeTab != null && activeTab.uuid == widget.uuid) {
          _updateResponseController();
        }

        return BlocBuilder<RequestBloc, RequestState>(
          builder: (context, requestState) {
            if (requestState is RequestLoadingState) {
              return const Center(child: CircularProgressIndicator());
            } else if (requestState is RequestErrorState) {
              _codeController.text =
                  const JsonEncoder.withIndent('  ').convert(requestState.error);
            } else if (requestState is RequestSuccessState) {
              context.read<HomeBloc>().add(
                UpdateResponseJsonDataEventTab(requestState.response),
              );
            }
            return _buildResponseView();
          },
        );
      },
    );
  }

  Widget _buildResponseView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 35.0),
              child: Text(
                "Response",
                style: TextStyle(fontSize: 14, color: Colors.white),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: IconButton(
                icon: const Icon(Icons.search, size: 20, color: Colors.white),
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (_) => AlertDialog(
                      title: const Text("Search"),
                      content: CodeFindPanelView(controller: _findController, readOnly: true),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        Expanded(
          child: CodeEditor(
            controller: _codeController,
            style: CodeEditorStyle(
              codeTheme: CodeHighlightTheme(
                languages: {'json': CodeHighlightThemeMode(mode: langJson)},
                theme: atomOneLightTheme,
              ),
            ),
            indicatorBuilder: (context, editingController, chunkController, notifier) {
              return Row(
                children: [
                  DefaultCodeLineNumber(controller: editingController, notifier: notifier),
                  DefaultCodeChunkIndicator(width: 20, controller: chunkController, notifier: notifier),
                ],
              );
            },
            chunkAnalyzer: DefaultCodeChunkAnalyzer(),
            scrollController: CodeScrollController(
              verticalScroller: ScrollController(),
              horizontalScroller: ScrollController(),
            ),
          ),
        ),
      ],
    );
  }
}*/



class CodeFindPanelView extends StatelessWidget {
  final CodeFindController controller;
  final bool readOnly;

  const CodeFindPanelView({
    super.key,
    required this.controller,
    required this.readOnly,
  });

  @override
  Widget build(BuildContext context) {
    TextEditingController searchController = TextEditingController();

    return Container(
      padding: const EdgeInsets.all(8),
      color: Colors.grey[900],
      child: Row(
        children: [
          // Search Input Field
          Expanded(
            child: TextField(
              controller: searchController,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                hintText: 'Search...',
                hintStyle: TextStyle(color: Colors.white54),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                controller.value; // Search in the editor
              },
            ),
          ),
          // Search Navigation Buttons
          IconButton(
            icon: const Icon(Icons.arrow_upward, color: Colors.white),
            onPressed: controller.previousMatch,
          ),
          IconButton(
            icon: const Icon(Icons.arrow_downward, color: Colors.white),
            onPressed: controller.nextMatch,
          ),
          // Close Button
          IconButton(
            icon: const Icon(Icons.close, color: Colors.white),
            onPressed: () {
              controller.close();
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }
}








//old
/*class ResponseView extends StatefulWidget {
  const ResponseView({super.key});

  @override
  State<ResponseView> createState() => _ResponseViewState();
}

class _ResponseViewState extends State<ResponseView> {

  final Map<String, dynamic> jsonData = {
    "status": 200,
    "message": "User data retrieved successfully",
    "data": {
      "id": 123,
      "name": "John Doe",
      "email": "<EMAIL>",
      "created_at": "2024-01-10T12:34:56Z",
    }
  };

  @override
  Widget build(BuildContext context) {


    final responseController = CodeController(
      text: ''' {"status": 200,
    "message": "User data retrieved successfully",
    "data": {
      "id": 123,
      "name": "John Doe",
      "email": "<EMAIL>",
      "created_at": "2024-01-10T12:34:56Z"
    }} ''',
      language: json,);


    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
       // Divider(height: 0.5, color: AppThemeColor.dividerBackgroundColor,),
        CommonText(
          text: "Response",
          textStyle: mMediumTextStyle16(
              textSize: 14, textColor: AppThemeColor.white),
        ),

        SizedBox(height: 23,),

        Flexible(
          child: CodeTheme(
            data: CodeThemeData(styles: a11yDarkTheme),
            child: CodeField(
              background: AppThemeColor.commonBackground,
              //decoration: BoxDecoration(border: ),
              //onChanged: onChanged,
              //gutterStyle: GutterStyle.none,
              controller: responseController,
              readOnly: true,
              expands: true,
              wrap: true,
              textStyle: mRegularTextStyle16(
                  textSize: 12),
            ),
          ),
        ),

      ],
    );
  }
}*/




// CommonText(
// text: JsonEncoder.withIndent('  ').convert(jsonData),
// maxLine: 200000,
// textStyle: mMediumTextStyle16(
// textSize: 12, textColor: AppThemeColor.tableTextColor),
// ),