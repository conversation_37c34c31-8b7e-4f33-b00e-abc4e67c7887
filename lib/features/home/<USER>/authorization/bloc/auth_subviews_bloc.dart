import 'package:bloc/bloc.dart';
import 'package:postman_flutter/features/home/<USER>/authorization/api_key/api_key_data.dart';
import 'package:postman_flutter/features/home/<USER>/authorization/bearer_token/bearer_token_data.dart';
import '../basic_auth/basic_auth_data.dart';
import '../jwt_bearer/jwt_bearer_data.dart';
import 'auth_subviews_event.dart';
import 'auth_subviews_state.dart';


class AuthSubViewsBloc extends Bloc<AuthSubViewsEvent, AuthSubViewsState> {
  AuthSubViewsBloc()
      : super(AuthSubViewsState(
    authType: "No Auth",
    basicAuthFields: BasicAuthFields(username: '', password: ''),
    bearerTokenFields: BearerTokenFields(token: ''),
    apiKeyFields: ApiKeyFields(key: '', value: '', addToHeaderOrQueryParams: 'Header'),
    jwtBearerFields: JwtBearerFields(
      algorithm: '',
      secretToken: '',
      payload: '{}',
      requestHeaderprefix: 'Bearer',
      jwtHeader: '{}',
      jwtTokenToHeaderOrQueryParams: 'Request Header',
    ),
  )) {
    on<AuthTypeChanged>((event, emit) {
      print('authEV: ${event.authType}');
      emit(state.copyWith(authType: event.authType));
      print('authST: ${state.authType}');
    });

    on<BasicAuthFieldsChanged>((event, emit) {
      emit(state.copyWith(basicAuthFields: event.bsicAuthFields));
    });

    on<BearerTokenFieldsChanged>((event, emit) {
      emit(state.copyWith(bearerTokenFields: event.bearerTokenFields));
    });

    on<ApiKeyFieldsChanged>((event, emit) {
      emit(state.copyWith(apiKeyFields: event.apiKeyFields));
    });

    // Add this handler for JwtBearerFieldsChanged
    on<JwtBearerFieldsChanged>((event, emit) {
      emit(state.copyWith(jwtBearerFields: event.jwtBearerFields));
    });
  }
}



/*class AuthSubViewsBloc extends Bloc<AuthSubViewsEvent, AuthSubViewsState> {
  AuthSubViewsBloc()
      : super(AuthSubViewsState(
    authType: "Inherit auth from parent", // Set the default value here
    basicAuthFields: BasicAuthFields(username: '', password: ''),
    bearerTokenFields: BearerTokenFields(token: ''),
    apiKeyFields: ApiKeyFields(key: '', value: '', addToHeaderOrQueryParams: 'Header'),
    jwtBearerFields: JwtBearerFields(
      algorithm: '',
      secretToken: '',
      payload: '{}',
      requestHeaderprefix: 'Bearer',
      jwtHeader: '{}',
      jwtTokenToHeaderOrQueryParams: 'Request Header',
    ),
  )) {
    on<AuthTypeChanged>((event, emit) {
      print('authEV: ${event.authType}');
      emit(state.copyWith(authType: event.authType));
      print('authST: ${state.authType}');
    });

    on<BasicAuthFieldsChanged>((event, emit) {
      emit(state.copyWith(basicAuthFields: event.bsicAuthFields));
    });

    on<BearerTokenFieldsChanged>((event, emit) {
      emit(state.copyWith(bearerTokenFields: event.bearerTokenFields));
    });

    on<ApiKeyFieldsChanged>((event, emit) {
      emit(state.copyWith(apiKeyFields: event.apiKeyFields));
    });
  }
}*/
