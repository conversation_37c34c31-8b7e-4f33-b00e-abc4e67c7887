
import 'dart:convert' as dc;
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_code_editor/flutter_code_editor.dart';
import 'package:flutter_highlight/themes/a11y-dark.dart';
import 'package:flutter_highlight/themes/atom-one-light.dart';
import '../../../../../helpers/color_config.dart';
import '../../../../../helpers/style_config.dart';
import '../../../../../helpers/debouncer.dart';
import '../../../../common_widgets/dynamic_text_area.dart';
import '../../../bloc/home_bloc.dart';
import '../../../bloc/home_event.dart';
import '../../../tab_manager.dart';
import '../json_input_editor.dart';
import 'package:highlight/languages/json.dart' show json;
import 'bloc/json_data_bloc.dart';
import 'bloc/json_data_event.dart';
import 'bloc/json_data_state.dart';



class JsonDataView extends StatefulWidget {

  final TextEditingController jsonController;

  const JsonDataView({required this.jsonController, super.key});

  @override
  _JsonDataViewState createState() => _JsonDataViewState();
}

class _JsonDataViewState extends State<JsonDataView> {
  late final CodeController _codeController;

  @override
  void initState() {
    super.initState();
    _codeController = CodeController(
      text: widget.jsonController.text,
      language: json,
    );

    // Use a debounce mechanism to avoid processing changes too frequently
    var debouncer = Debouncer(milliseconds: 300);

    _codeController.addListener(() {
      // Debounce the text changes to avoid excessive processing
      debouncer.run(() {
        var newData = _codeController.text;
        if (newData.isNotEmpty) {
          // Process JSON in a separate microtask to avoid blocking the UI
          Future.microtask(() {
            try {
              // More efficient JSON parsing
              Map<String, dynamic> jsonn = dc.json.decode(newData);

              // Update state only if widget is still mounted
              if (mounted) {
                context.read<JsonDataBloc>().add(UpdateJsonDataEvent(jsonn));
                context.read<HomeBloc>().add(UpdateJsonDataEventTab(jsonn));
              }
            } catch (e) {
              debugPrint("JSON parsing error: $e");
            }
          });
        }
      });
    });

  }

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return  Column(
      children: [
        Container(
          constraints: const BoxConstraints(minHeight: 200, maxHeight: 400),
          child: CodeTheme(
            data: CodeThemeData(styles: a11yDarkTheme),
            child: CodeField(
              background: AppThemeColor.commonBackground,
              controller: _codeController,
              expands: true,
              wrap: true,
              textStyle: mRegularTextStyle16(textSize: 12),
              //onChanged: (value){}, // implement UpdateJsonDataEventTab in this onChanged
            ),
          ),
        ),
      ],
    );
  }
}



/*class JsonDataView extends StatefulWidget {
  final TextEditingController jsonController;

  const JsonDataView({required this.jsonController, super.key});

  @override
  _JsonDataViewState createState() => _JsonDataViewState();
}

class _JsonDataViewState extends State<JsonDataView> {
  late final CodeLineEditingController _editorController;

  @override
  void initState() {
    super.initState();
    _editorController = CodeLineEditingController.fromText(widget.jsonController.text);

    _editorController.addListener(() {
      var newData = _editorController.text;
      if (newData.isNotEmpty) {
        try {
          Map<String, dynamic> jsonn = dc.jsonDecode(newData);
          context.read<JsonDataBloc>().add(UpdateJsonDataEvent(jsonn));
          context.read<HomeBloc>().add(UpdateJsonDataEventTab(jsonn));
        } catch (e) {
          debugPrint("$e");
        }
      }
    });
  }

  @override
  void dispose() {
    _editorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          constraints: const BoxConstraints(minHeight: 200, maxHeight: 400),
          decoration: BoxDecoration(
            color: AppThemeColor.commonBackground,
            borderRadius: BorderRadius.circular(8),
          ),
          child: CodeEditor(
            controller: _editorController,
            style: CodeEditorStyle(
              codeTheme: CodeHighlightTheme(
                languages: {
                  'json': CodeHighlightThemeMode(mode: langJson),
                },
                theme: atomOneLightTheme,
              ),
            ),
            indicatorBuilder: (context, editingController, chunkController, notifier) {
              return Row(
                children: [
                  DefaultCodeLineNumber(controller: editingController, notifier: notifier),
                  DefaultCodeChunkIndicator(width: 20, controller: chunkController, notifier: notifier),
                ],
              );
            },
            chunkAnalyzer: DefaultCodeChunkAnalyzer(),
          ),
        ),
      ],
    );
  }
}*/















/*class JsonData extends StatefulWidget {
  const JsonData({
    super.key,

  });



  @override
  State<JsonData> createState() => _JsonDataState();
}

class _JsonDataState extends State<JsonData> {




  @override
  Widget build(BuildContext context) {

   final jsonController = CodeController(
        text: ''' {"key":"value"} ''',
        language: json,);

    return Column(
      children: [
        Container(
          //color: Colors.grey,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(0),
            color: AppThemeColor.commonBackground,
            //border: Border.all(color: AppThemeColor.dividerBackgroundColor),
            border: Border(top: BorderSide(color: AppThemeColor.dividerBackgroundColor, width: 1),
                left: BorderSide(color: AppThemeColor.dividerBackgroundColor, width: 1)
            ),
          ),
          margin: EdgeInsets.only(left: 0, right: 0),
          //width: 400,
          //height: 180,
          constraints: new BoxConstraints(
            minHeight: 200.0,
            maxHeight: 400.0,
          ),
          child:  CodeTheme(
            data: CodeThemeData(styles: a11yDarkTheme),
            child: CodeField(
              background: AppThemeColor.commonBackground,
              //decoration: BoxDecoration(border: ),
              //onChanged: onChanged,
              //gutterStyle: GutterStyle.none,
              controller: jsonController,
              expands: true,
              wrap: true,
              textStyle: mRegularTextStyle16(
                  textSize: 12),
            ),
          )

        ),
      ],
    );
  }
}*/
