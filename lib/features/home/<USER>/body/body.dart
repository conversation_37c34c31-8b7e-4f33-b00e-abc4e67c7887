import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/api_collection.dart';

import '../../../../helpers/color_config.dart';
import '../../../../helpers/style_config.dart';
import '../../../common_widgets/CommonText.dart';
import '../../../common_widgets/dynamic_radio_tabs/dynamic_radio_tabs_widget.dart';
import '../../bloc/home_bloc.dart';
import '../../bloc/home_state.dart';
import 'form_data/form_data.dart';
import 'json_data/json_data.dart';

class Body extends StatefulWidget {
  final String uuid;
  final TabModel? tabModel;

  Body({required this.uuid, this.tabModel,super.key});

  @override
  State<Body> createState() => _BodyState();
}

class _BodyState extends State<Body> {
  late TextEditingController _jsonController;

  @override
  void initState() {
    super.initState();
    _jsonController = TextEditingController();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateControllersFromActiveTab();
  }

  void _updateControllersFromActiveTab() {
    final activeTab = context.read<HomeBloc>().state.activeTab;
    if (activeTab != null && activeTab.uuid == widget.uuid) {
      _jsonController.text = jsonEncode(activeTab.jsonData ?? {});
    } else {
      _jsonController.text = "";
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        final activeTab = state.activeTab;
        _jsonController.text = jsonEncode(activeTab?.jsonData);
        if (activeTab == null || activeTab.uuid != widget.uuid) {
          return Container(); 
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Container(
                margin: EdgeInsets.zero,
                padding: const EdgeInsets.only(bottom: 0, left: 0),
                child: SingleChildScrollView(
                  child: DynamicRadioTabsWidget(
                    tabTitles: ['None', 'Form-data', 'JSON'],
                    tabViews: [
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.only(top: 80.0),
                          child: CommonText(
                            text: "This request does not have a body",
                            textStyle: mMediumTextStyle16(
                                textSize: 14, textColor: AppThemeColor.white),
                          ),
                        ),
                      ),
                      FormData(),
                      JsonDataView(
                        key: ValueKey(widget.uuid),
                        jsonController: _jsonController,
                        tabModel: widget.tabModel,
                      ),
                    ],
                    defaultSelectedTab: 0,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}



