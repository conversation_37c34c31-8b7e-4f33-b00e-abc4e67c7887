import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/postmanrequest/postman_request_event.dart';
import 'package:postman_flutter/features/home/<USER>/postmanrequest/postman_request_state.dart';
import 'package:postman_flutter/repo/postmanRequestRepo.dart';


class PostmanRequestBloc extends Bloc<PostmanRequestEvent, PostmanRequestState> {
  final PostmanRequestRepository repository;

  PostmanRequestBloc({required this.repository}) : super(PostmanRequestInitial()) {
    on<CreatePostmanRequest>(_onCreateRequest);
  }

  Future<void> _onCreateRequest(
    CreatePostmanRequest event,
    Emitter<PostmanRequestState> emit,
  ) async {
    try {
      emit(PostmanRequestCreating());
      
      debugPrint('Creating request: ${event.name}, method: ${event.method}');
      
      final response = await repository.createRequest(
        name: event.name,
        url: event.url,
        method: event.method,
        collectionId: event.collectionId,
        folderId: event.folderId,
        workspaceId: event.workspaceId,
      );
      print('request create response: ${jsonEncode(object)}');
      if (response.success && response.data != null) {
        debugPrint('Request created successfully: ${response.data!.id}');
        emit(PostmanRequestCreated(
          request: response.data!,
          message: response.message,
        ));
      } else {
        debugPrint('Failed to create request: ${response.message}');
        emit(PostmanRequestCreationError(message: response.message));
      }
    } catch (e) {
      debugPrint('Error creating request: $e');
      emit(PostmanRequestCreationError(message: 'Error creating request: $e'));
    }
  }
}
