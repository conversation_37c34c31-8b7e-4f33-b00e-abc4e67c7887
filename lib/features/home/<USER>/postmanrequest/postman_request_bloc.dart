import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/postmanrequest/postman_request_event.dart';
import 'package:postman_flutter/features/home/<USER>/postmanrequest/postman_request_state.dart';
import 'package:postman_flutter/repo/postmanRequestRepo.dart';


class PostmanRequestBloc extends Bloc<PostmanRequestEvent, PostmanRequestState> {
  final PostmanRequestRepository repository;

  PostmanRequestBloc({required this.repository}) : super(PostmanRequestInitial()) {
    on<CreatePostmanRequest>(_onCreateRequest);
    on<FetchRequestById>(_onFetchRequestById);
    on<SendDirectRequest>(_onSendDirectRequest);
    on<SaveRequest>(_onSaveRequest);
    on<FetchRequestDetails>(_onFetchRequestDetails);
  }

  Future<void> _onCreateRequest(
    CreatePostmanRequest event,
    Emitter<PostmanRequestState> emit,
  ) async {
    try {
      emit(PostmanRequestCreating());

      debugPrint('Creating request: ${event.name}, method: ${event.method}');

      final response = await repository.createRequest(
        name: event.name,
        url: event.url,
        method: event.method,
        collectionId: event.collectionId,
        folderId: event.folderId,
        workspaceId: event.workspaceId,
      );
      debugPrint('request create response: ${jsonEncode(response)}');

      if (response.status == 'Success' && response.data != null) {
        debugPrint('Request created successfully: ${response.data!.id}');
        int? collectionId;
        int? folderId;

        if (event.collectionId != null && event.collectionId!.isNotEmpty) {
          collectionId = int.tryParse(event.collectionId!);
        }

        if (event.folderId != null && event.folderId!.isNotEmpty) {
          folderId = int.tryParse(event.folderId!);
        }

        emit(PostmanRequestCreated(
          request: response.data!,
          message: response.msg,
          collectionId: collectionId,
          folderId: folderId,
        ));
      } else {
        debugPrint('Failed to create request: ${response.msg}');
        emit(PostmanRequestCreationError(message: response.msg));
      }
    } catch (e) {
      debugPrint('Error creating request: $e');
      emit(PostmanRequestCreationError(message: 'Error creating request: $e'));
    }
  }

  Future<void> _onFetchRequestById(
    FetchRequestById event,
    Emitter<PostmanRequestState> emit,
  ) async {
    try {
      emit(PostmanRequestFetching());

      debugPrint('Fetching request details for ID: ${event.requestId}');

      final response = await repository.getRequestById(
        requestId: event.requestId,
      );
      debugPrint('Request details response: ${jsonEncode(response)}');

      if (response.status == 'Success' && response.data != null) {
        debugPrint('Request details fetched successfully: ${response.data!.id}');

        emit(PostmanRequestFetched(
          request: response.data!,
          message: response.msg,
          collectionName: event.collectionName,
          folderName: event.folderName,
        ));
      } else {
        debugPrint('Failed to fetch request details: ${response.msg}');
        emit(PostmanRequestFetchError(message: response.msg));
      }
    } catch (e) {
      debugPrint('Error fetching request details: $e');
      emit(PostmanRequestFetchError(message: 'Error fetching request details: $e'));
    }
  }

  Future<void> _onSendDirectRequest(SendDirectRequest event,Emitter<PostmanRequestState> emit) async {
    try {
      emit(SendingDirectRequest());

      debugPrint('Sending direct request: ${event.method} ${event.url}');
      debugPrint('Headers: ${event.headers}');
      //debugPrint('Body: ${event.body}');

      final response = await repository.sendRequest(
        method: event.method,
        url: event.url,
        headers: event.headers,
        body: event.body,
        tabUuid: event.tabUuid,
      );

      //debugPrint('Direct request response: ${jsonEncode(response)}');

      if (response['status'] == 'success') {
        emit(DirectRequestSent(
          response: response,
          tabUuid: event.tabUuid,
        ));
      } else {
        emit(DirectRequestError(
          message: response['msg'] ?? 'Failed to send request',
          tabUuid: event.tabUuid,
        ));
      }
    } catch (e) {
      debugPrint('Error sending direct request: $e');
      emit(DirectRequestError(
        message: 'Error sending direct request: $e',
        tabUuid: event.tabUuid,
      ));
    }
  }

  Future<void> _onSaveRequest(SaveRequest event,Emitter<PostmanRequestState> emit) async {
    try {
      emit(SavingRequest());

      debugPrint('Saving request: ${event.name}, method: ${event.method}, url: ${event.url}');

      final response = await repository.saveRequest(
        name: event.name,
        url: event.url,
        method: event.method,
        headers: event.headers,
        body: event.body,
        params: event.params,
        auth: event.auth,
        collectionId: event.collectionId,
      );

      //debugPrint('Save request response: ${jsonEncode(response)}');

      if (response['status'] == 'Success' || response['status'] == 'success') {
        emit(RequestSaved(
          response: response,
          tabUuid: event.tabUuid,
        ));
      } else {
        emit(RequestSaveError(
          message: response['message'] ?? response['msg'] ?? 'Failed to save request',
          tabUuid: event.tabUuid,
        ));
      }
    } catch (e) {
      debugPrint('Error saving request: $e');
      emit(RequestSaveError(
        message: 'Error saving request: $e',
        tabUuid: event.tabUuid,
      ));
    }
  }

  Future<void> _onFetchRequestDetails(
    FetchRequestDetails event,
    Emitter<PostmanRequestState> emit,
  ) async {
    try {
      emit(FetchingRequestDetails());

      debugPrint('Fetching request details for ID: ${event.requestId}');

      final response = await repository.getRequestDetails(
        requestId: event.requestId,
      );

      //debugPrint('Request details response: ${response.status}');

      if (response.status == 'Success' && response.data != null) {
        //debugPrint('Request details fetched successfully: ${response.data!.id}');

        emit(RequestDetailsFetched(
          requestDetails: response.data!,
          message: response.message,
          collectionName: event.collectionName,
          folderName: event.folderName,
        ));
      } else {
        debugPrint('Failed to fetch request details: ${response.message}');
        emit(RequestDetailsFetchError(message: response.message));
      }
    } catch (e) {
      debugPrint('Error fetching request details: $e');
      emit(RequestDetailsFetchError(message: 'Error fetching request details: $e'));
    }
  }
}
