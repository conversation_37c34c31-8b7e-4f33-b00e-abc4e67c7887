import 'package:equatable/equatable.dart';
import 'package:postman_flutter/model/request_model.dart';


abstract class PostmanRequestState extends Equatable {
  const PostmanRequestState();

  @override
  List<Object?> get props => [];
}

class PostmanRequestInitial extends PostmanRequestState {}

class PostmanRequestCreating extends PostmanRequestState {}

class PostmanRequestCreated extends PostmanRequestState {
  final RequestModel request;
  final String message;

  const PostmanRequestCreated({
    required this.request,
    required this.message,
  });

  @override
  List<Object?> get props => [request, message];
}

class PostmanRequestCreationError extends PostmanRequestState {
  final String message;

  const PostmanRequestCreationError({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}
