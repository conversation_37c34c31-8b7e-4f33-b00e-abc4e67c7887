import 'package:equatable/equatable.dart';

abstract class PostmanRequestEvent extends Equatable {
  const PostmanRequestEvent();

  @override
  List<Object?> get props => [];
}

class CreatePostmanRequest extends PostmanRequestEvent {
  final String name;
  final String url;
  final String method;
  final String? collectionId;
  final String? folderId;
  final int workspaceId;

  const CreatePostmanRequest({
    required this.name,
    required this.url,
    required this.method,
    this.collectionId,
    this.folderId,
    required this.workspaceId,
  });

  @override
  List<Object?> get props => [name, url, method, collectionId, folderId, workspaceId];
}
