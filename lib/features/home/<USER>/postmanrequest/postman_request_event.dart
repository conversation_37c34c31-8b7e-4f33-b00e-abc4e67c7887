import 'package:equatable/equatable.dart';

abstract class PostmanRequestEvent extends Equatable {
  const PostmanRequestEvent();

  @override
  List<Object?> get props => [];
}

class CreatePostmanRequest extends PostmanRequestEvent {
  final String name;
  final String url;
  final String method;
  final String? collectionId;
  final String? folderId;
  final int workspaceId;

  const CreatePostmanRequest({
    required this.name,
    required this.url,
    required this.method,
    this.collectionId,
    this.folderId,
    required this.workspaceId,
  });

  @override
  List<Object?> get props => [name, url, method, collectionId, folderId, workspaceId];
}

class FetchRequestById extends PostmanRequestEvent {
  final int requestId;
  final String? collectionName;
  final String? folderName;

  const FetchRequestById({
    required this.requestId,
    this.collectionName,
    this.folderName,
  });

  @override
  List<Object?> get props => [requestId, collectionName, folderName];
}

class SendDirectRequest extends PostmanRequestEvent {
  final String method;
  final String url;
  final Map<String, String>? headers;
  final Map<String, dynamic>? body;
  final String tabUuid;

  const SendDirectRequest({
    required this.method,
    required this.url,
    this.headers,
    this.body,
    required this.tabUuid,
  });

  @override
  List<Object?> get props => [method, url, headers, body, tabUuid];
}

class SaveRequest extends PostmanRequestEvent {
  final String name;
  final String url;
  final String method;
  final Map<String, String> headers;
  final dynamic body;
  final Map<String, dynamic> params;
  final Map<String, dynamic> auth;
  final String? collectionId;
  final String tabUuid;

  const SaveRequest({
    required this.name,
    required this.url,
    required this.method,
    required this.headers,
    required this.body,
    required this.params,
    required this.auth,
    this.collectionId,
    required this.tabUuid,
  });

  @override
  List<Object?> get props => [name, url, method, headers, body, params, auth, collectionId, tabUuid];
}

class FetchRequestDetails extends PostmanRequestEvent {
  final int requestId;
  final String? collectionName;
  final String? folderName;

  const FetchRequestDetails({
    required this.requestId,
    this.collectionName,
    this.folderName,
  });

  @override
  List<Object?> get props => [requestId, collectionName, folderName];
}
