import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:postman_flutter/model/folder_model.dart';
import 'package:postman_flutter/repo/folderRepo.dart';

part 'folder_event.dart';
part 'folder_state.dart';

class FolderBloc extends Bloc<FolderEvent, FolderState> {
  final FolderRepository repository;

  FolderBloc({required this.repository}) : super(FolderInitial()) {
    on<CreateFolder>(_onCreateFolder);
    on<UpdateFolder>(_onUpdateFolder);
    on<DeleteFolder>(_onDeleteFolder);
  }

  Future<void> _onCreateFolder(CreateFolder event, Emitter<FolderState> emit) async {
    emit(FolderCreating());

    final connectivity = await Connectivity().checkConnectivity();
    if (connectivity.contains(ConnectivityResult.none)) {
      emit(const FolderCreationError('No internet connection'));
      return;
    }

    try {
      final apiResponse = await repository.createFolder(
        name: event.name,
        description: event.description,
        collectionId: event.collectionId,
        parentFolderId: event.parentFolderId,
      );
      debugPrint('API RESPONSE --> ${jsonEncode(apiResponse)}');
      if (apiResponse.status == 'Success' && apiResponse.data != null) {
        // Get the newly created folder from the response
        FolderModel? newFolder = apiResponse.getSingleFolder();

        if (newFolder != null) {
          // Log the folder data for debugging
          debugPrint('Created folder: id=${newFolder.id}, name=${newFolder.name}, parentId=${newFolder.parentFolderId}');

          // Convert FolderModel to Folder for UI
          final folder = newFolder.toFolder();
          debugPrint('Converted to Folder: id=${folder.id}, name=${folder.name}, parentId=${folder.parentFolderId}');

          // Make sure the folder name is not null or empty
          if (folder.name == null || folder.name!.isEmpty) {
            debugPrint('WARNING: Folder name is null or empty, using fallback name');
            folder.name = "New Folder";
            // Also update the original FolderModel
            newFolder.name = "New Folder";
          }

          // Emit the success state with the new folder
          emit(FolderCreated('Folder created successfully', newFolder));
        } else {
          emit(const FolderCreationError('Failed to parse folder data'));
        }
      } else {
        emit(FolderCreationError(apiResponse.msg ?? 'Failed to create folder'));
      }
    } catch (e) {
      debugPrint('Error creating folder: $e');
      emit(FolderCreationError(e.toString()));
    }
  }

  Future<void> _onUpdateFolder(UpdateFolder event, Emitter<FolderState> emit) async {
    emit(FolderUpdating());

    final connectivity = await Connectivity().checkConnectivity();
    if (connectivity.contains(ConnectivityResult.none)) {
      emit(const FolderUpdateError('No internet connection'));
      return;
    }

    try {
      final apiResponse = await repository.updateFolder(
        folderId: event.folderId,
        name: event.name,
        description: event.description,
        collectionId: event.collectionId,
        parentFolderId: event.parentFolderId,
      );

      if (apiResponse.status == 'Success' && apiResponse.data != null) {
        // Get the updated folder from the response using the helper method
        final updatedFolder = apiResponse.getSingleFolder();

        if (updatedFolder != null) {
          // Log the folder data for debugging
          debugPrint('Updated folder: id=${updatedFolder.id}, name=${updatedFolder.name}, parentId=${updatedFolder.parentFolderId}');

          // Convert FolderModel to Folder for UI
          final folder = updatedFolder.toFolder();
          debugPrint('Converted to Folder: id=${folder.id}, name=${folder.name}, parentId=${folder.parentFolderId}');

          // Make sure the folder name is not null or empty
          if (folder.name == null || folder.name!.isEmpty) {
            debugPrint('WARNING: Folder name is null or empty, using fallback name');
            folder.name = "Updated Folder";
            // Also update the original FolderModel
            updatedFolder.name = "Updated Folder";
          }

          emit(FolderUpdated('Folder updated successfully', updatedFolder));
        } else {
          emit(const FolderUpdateError('Failed to parse folder data'));
        }
      } else {
        emit(FolderUpdateError(apiResponse.msg ?? 'Failed to update folder'));
      }
    } catch (e) {
      // Log the error
      debugPrint('Error updating folder: $e');
      emit(FolderUpdateError(e.toString()));
    }
  }

  Future<void> _onDeleteFolder(DeleteFolder event, Emitter<FolderState> emit) async {
    emit(FolderDeleting());

    final connectivity = await Connectivity().checkConnectivity();
    if (connectivity.contains(ConnectivityResult.none)) {
      emit(const FolderDeleteError('No internet connection'));
      return;
    }

    try {
      final apiResponse = await repository.deleteFolder(event.folderId);

      if (apiResponse.status == 'Success') {
        emit(const FolderDeleted('Folder deleted successfully'));
      } else {
        emit(FolderDeleteError(apiResponse.msg ?? 'Failed to delete folder'));
      }
    } catch (e) {
      emit(FolderDeleteError(e.toString()));
    }
  }
}
