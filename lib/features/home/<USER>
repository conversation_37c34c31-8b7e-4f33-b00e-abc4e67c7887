import 'package:flutter/material.dart';
import 'package:multi_split_view/multi_split_view.dart';
import 'package:postman_flutter/api_constant.dart';
import 'package:postman_flutter/features/home/<USER>/collection/collection_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/folder/folder_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/postmanrequest/postman_request_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/postmanrequest/postman_request_event.dart';
import 'package:postman_flutter/features/home/<USER>/postmanrequest/postman_request_state.dart';
import 'package:postman_flutter/helpers/common.dart';
import 'package:postman_flutter/model/collection_detail_model.dart';
import 'package:postman_flutter/model/collection_model.dart';
import 'package:postman_flutter/model/folder_model.dart';
import 'package:postman_flutter/repo/collectionRepo.dart';
import 'package:postman_flutter/repo/folderRepo.dart';
import 'package:uuid/uuid.dart';
import '../../globals.dart';
import '../../helpers/color_config.dart';
import '../../helpers/style_config.dart';
import '../common_widgets/CommonAlertDialog.dart';
import '../common_widgets/CommonIconTextButton.dart';
import '../common_widgets/CommonSVGIcon.dart';
import '../common_widgets/CommonText.dart';
import '../common_widgets/CommonTextFormField.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'bloc/home_bloc.dart';
import 'bloc/home_event.dart';
import 'bloc/home_state.dart';
import 'data/api_collection.dart';

class CollectionPanel extends StatefulWidget {
  const CollectionPanel({super.key});

  @override
  State<CollectionPanel> createState() => _CollectionPanelState();
}

class _CollectionPanelState extends State<CollectionPanel> {
  final searchController = TextEditingController();

  final collectionNameController = TextEditingController();

  final methodController = TextEditingController();

  final methodController2 = TextEditingController();

  final requestTitleController = TextEditingController();

  final requestTitleController2 = TextEditingController();

  final addFolder1Controller = TextEditingController();

  final addFolder2Controller = TextEditingController();

  // Maps to track expanded collections and folders by their ID
  final Map<int, bool> expandedCollections = {};
  final Map<int, bool> expandedFolders = {};

  // Variables to track the last folder creation
  int? lastCollectionId;
  int? lastParentFolderId;

  // Variable to store the filtered collections
  List<Collection> filteredCollections = [];

  late HomeBloc homeBloc;

  @override
  void initState() {
    super.initState();
    homeBloc = BlocProvider.of<HomeBloc>(context, listen: false);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CollectionBloc>().add(FetchCollections(ApiConstant.cmsWorkspaceId));
    });
    searchController.addListener(_filterCollections);
  }

  @override
  void dispose() {
    // Clean up the controller when the widget is disposed
    searchController.removeListener(_filterCollections);
    searchController.dispose();
    super.dispose();
  }

  // Filter collections based on search query
  void _filterCollections() {
    // This will be called when the search text changes
    homeBloc.add(SearchQueryChangedEvent(searchController.text));
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<HomeBloc, HomeState>(
          listenWhen: (previous, current) => previous.searchQuery != current.searchQuery,
          listener: (context, state) {
            setState(() {
            });
          },
        ),
        BlocListener<CollectionBloc, CollectionState>(
          listener: (context, state) {
            if (state is CollectionCreated) {
              // Show success message
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.green,
                ),
              );
              context.read<CollectionBloc>().add(FetchCollections(ApiConstant.cmsWorkspaceId));
            } else if (state is CollectionCreationError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            } else if (state is CollectionDeleted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.green,
                ),
              );
              context.read<CollectionBloc>().add(FetchCollections(ApiConstant.cmsWorkspaceId));
            } else if (state is CollectionDeletionError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            } else if (state is CollectionUpdated) {
              // Show success message
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.green,
                ),
              );
              // Refresh the collections list to update the collection
              context.read<CollectionBloc>().add(FetchCollections(ApiConstant.cmsWorkspaceId));
            } else if (state is CollectionUpdateError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        ),
        BlocListener<PostmanRequestBloc, PostmanRequestState>(
          listener: (context, state) {
            if (state is PostmanRequestCreated) {
              // Show success message
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.green,
                ),
              );
              final collectionBloc = context.read<CollectionBloc>();
              collectionBloc.add(FetchCollections(ApiConstant.cmsWorkspaceId));

              setState(() {
                if (state.collectionId != null) {
                  debugPrint('Expanding collection ${state.collectionId} after request creation');
                  expandedCollections[state.collectionId!] = true;
                }
                if (state.folderId != null) {
                  debugPrint('Expanding folder ${state.folderId} after request creation');
                  expandedFolders[state.folderId!] = true;
                }

                // Also expand any parent collections/folders if we have them stored
                if (lastCollectionId != null) {
                  expandedCollections[lastCollectionId!] = true;
                }
                if (lastParentFolderId != null) {
                  expandedFolders[lastParentFolderId!] = true;
                }
              });
              debugPrint('Refreshing collection detail after request creation');

              final collectionBlocRef = collectionBloc;
              Future.delayed(const Duration(milliseconds: 500), () {
                if (mounted) {
                  collectionBlocRef.add(FetchCollections(ApiConstant.cmsWorkspaceId));
                }
              });

              // Fetch the newly created request details to open it in a tab
              final postmanRequestBloc = context.read<PostmanRequestBloc>();
              ApiConstant.requestID = state.request.id;
              postmanRequestBloc.add(FetchRequestById(
                requestId: state.request.id,
                collectionName: state.collectionId != null ?
                    context.read<CollectionBloc>().state is CollectionLoaded ?
                        (context.read<CollectionBloc>().state as CollectionLoaded).collections
                            .firstWhere((c) => c.collectionId == state.collectionId,
                                orElse: () => Collection()).collectionTitle : null : null,
                folderName: null, 
              ));

            } else if (state is PostmanRequestCreationError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            } else if (state is PostmanRequestFetched) {
              final requestData = state.request;
              ApiConstant.requestID = requestData.id;
              Map<String, String> headers = {};
              if (requestData.headers.isNotEmpty) {
                requestData.headers.forEach((key, value) {
                  if (value is String) {
                    headers[key] = value;
                  } else {
                    headers[key] = value.toString();
                  }
                });
              }

              Map<String, dynamic> jsonData = {};

              final tabModel = TabModel(
                uuid: Uuid().v4(),
                tabName: requestData.name,
                method: requestData.method,
                url: requestData.url,
                jsonData: jsonData,
                headers: headers,
                collectionName: state.collectionName,
                folderName: state.folderName,
              );
              homeBloc.add(OpenTabEvent(tabModel: tabModel)); //rohan

            } else if (state is PostmanRequestFetchError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            } else if (state is RequestDetailsFetched) {
              final requestDetails = state.requestDetails;
              Map<String, String> headers = {};
              if (requestDetails.headers.isNotEmpty) {
                requestDetails.headers.forEach((key, value) {
                  if (value is String) {
                    headers[key] = value;
                  } else {
                    headers[key] = value.toString();
                  }
                });
              }
              debugPrint('Request details: ${requestDetails.headers} ++++++++ headers: $headers');
              Map<String, dynamic> jsonData = {};
              if (requestDetails.body.type == 'json') {
                try {
                  jsonData = requestDetails.body.getContentAsJson();
                } catch (e) {
                  debugPrint('Error parsing JSON body: $e');
                }
              }

              final tabModel = TabModel(
                uuid: Uuid().v4(),
                tabName: requestDetails.name,
                method: requestDetails.method,
                url: requestDetails.url,
                jsonData: jsonData,
                headers: headers,
                collectionName: state.collectionName,
                folderName: state.folderName,
              );

              homeBloc.add(OpenTabEvent(tabModel: tabModel));
              

            } else if (state is RequestDetailsFetchError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        ),
        BlocListener<FolderBloc, FolderState>(
          listener: (context, state) {
            if (state is FolderCreated) {
              // Show success message
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.green,
                ),
              );

              // Expand the parent collection or folder
              setState(() {
                // If we have a parent folder ID, expand it
                if (lastParentFolderId != null) {
                  expandedFolders[lastParentFolderId!] = true;
                }

                // If we have a collection ID, expand it
                if (lastCollectionId != null) {
                  expandedCollections[lastCollectionId!] = true;
                }
              });

              // Refresh the collections list to include the new folder
              context.read<CollectionBloc>().add(FetchCollections(ApiConstant.cmsWorkspaceId));

              // If we have a parent folder ID or collection ID, refresh the collection detail
              if (lastParentFolderId != null || lastCollectionId != null) {
                // Force a refresh of the collection detail to show the new folder
                debugPrint('Refreshing collection detail after folder creation');

                // Store the collection bloc reference before the async gap
                final collectionBlocRef = context.read<CollectionBloc>();

                // Delay the refresh slightly to ensure the server has processed the change
                Future.delayed(const Duration(milliseconds: 500), () {
                  if (mounted && lastCollectionId != null) {
                    // Refresh the collection detail by triggering a new fetch using the stored reference
                    collectionBlocRef.add(FetchCollections(ApiConstant.cmsWorkspaceId));

                    // Expand the collection to show the new folder
                    setState(() {
                      if (lastCollectionId != null) {
                        expandedCollections[lastCollectionId!] = true;
                      }
                      if (lastParentFolderId != null) {
                        expandedFolders[lastParentFolderId!] = true;
                      }
                    });
                  }
                });
              }
            } else if (state is FolderCreationError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            } else if (state is FolderUpdated) {
              // Show success message
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.green,
                ),
              );

              // Refresh the collections list to update the folder
              context.read<CollectionBloc>().add(FetchCollections(ApiConstant.cmsWorkspaceId));

              // Force a refresh of the collection detail to show the updated folder
              debugPrint('Refreshing collection detail after folder update');

              // Delay the refresh slightly to ensure the server has processed the change
              // Store the collection bloc reference before the async gap
              final collectionBlocRef = context.read<CollectionBloc>();

              Future.delayed(const Duration(milliseconds: 500), () {
                if (mounted) {
                  // Refresh the collection detail by triggering a new fetch using the stored reference
                  collectionBlocRef.add(FetchCollections(ApiConstant.cmsWorkspaceId));
                }
              });
            } else if (state is FolderUpdateError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            } else if (state is FolderDeleted) {
              // Show success message
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.green,
                ),
              );

              // Refresh the collections list to remove the deleted folder
              final collectionBloc = context.read<CollectionBloc>();
              collectionBloc.add(FetchCollections(ApiConstant.cmsWorkspaceId));

              // Force a refresh of the collection detail to show the updated structure
              debugPrint('Refreshing collection detail after folder deletion');

              // Delay the refresh slightly to ensure the server has processed the change
              Future.delayed(const Duration(milliseconds: 500), () {
                if (mounted && lastCollectionId != null) {
                  // Refresh the collection detail by triggering a new fetch
                  collectionBloc.add(FetchCollections(ApiConstant.cmsWorkspaceId));
                }
              });
            } else if (state is FolderDeleteError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        ),
      ],
      child: BlocBuilder<HomeBloc, HomeState>(
        builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.only(left: 10.0),
          child: Column(
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CommonText(
                    text: 'Collections',
                    textStyle: mRegularTextStyle16(
                        textSize: 12, textColor: AppThemeColor.white),
                  ),
                  Row(
                    children: [
                      CommonIconTextButton(
                        text: 'New',
                        padding: EdgeInsets.zero,
                        textStyle: const TextStyle(
                            color: AppThemeColor.lightBlue, fontSize: 12),
                        icon: const Icon(
                          Icons.add,
                          color: AppThemeColor.lightBlue,
                          size: 16,
                        ),
                        callback: () {
                          _showAddCollectionDialog(context);
                        },
                      ),
                    ],
                  )
                ],
              ),

              const SizedBox(height: 0),

              // Search Input Field
              //if(filteredData.isNotEmpty)
              CommonTextFormField(
                hintTextString: "Search",
                isLabelText: false,
                fontSize: 14,
                padding:EdgeInsets.only(right: 0, left: 0, top: 10, bottom: 10),
                hintStyle: TextStyle(color: AppThemeColor.hintTextColor),
                textEditController: searchController,
                cornerRadius: Globals.defaultTextInputCornerRadius,
                isDense: true,
                errorMessage: "",
                onChange: (value) {
                  homeBloc.add(SearchQueryChangedEvent(value));
                },
              ),

              const SizedBox(height: 10),
              Expanded(
                child: BlocBuilder<CollectionBloc, CollectionState>(
                  builder: (context, state) {
                    if(state is CollectionInitial || state is CollectionLoading){
                      return const Center(child: CircularProgressIndicator());
                    }
                    else if(state is CollectionLoaded){
                      final searchQuery = homeBloc.state.searchQuery.toLowerCase();
                      final collections = state.collections;

                      final filteredCollections = searchQuery.isEmpty
                          ? collections
                          : collections.where((collection) => collection.collectionTitle?.toLowerCase().contains(searchQuery) ?? false).toList();

                      if (filteredCollections.isEmpty) {
                        return const Center(
                          child: Text(
                            'No collections found',
                            style: TextStyle(color: AppThemeColor.white),
                          ),
                        );
                      }

                      return ListView.builder(
                        itemCount: filteredCollections.length,
                        itemBuilder: (context, index) {
                          final collection = filteredCollections[index];
                          final isExpanded = expandedCollections[collection.collectionId] ?? false;
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              InkWell(
                                onTap: () {
                                  CommonConstants.collectionID = collection.collectionId ?? 0;
                                  final newExpandedState = !isExpanded;
                                  setState(() {
                                    expandedCollections[collection.collectionId ?? 0] = newExpandedState;
                                  });
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          CommonSVGIcon(
                                            imageName: isExpanded ? 'direction_down' : 'direction_right',
                                            imagePath: 'images',
                                            color: AppThemeColor.white,
                                            height: 24,
                                            width: 24,
                                          ),
                                          const SizedBox(width: 5),
                                          CommonText(
                                            text: collection.collectionTitle ?? 'N/A',
                                            textStyle: mRegularTextStyle16(
                                              textSize: 13, textColor: AppThemeColor.white
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 24,
                                        child: PopupMenuButton<String>(
                                          padding: EdgeInsets.zero,
                                          clipBehavior: Clip.none,
                                          color: AppThemeColor.lightBackground,
                                          icon: const CommonSVGIcon(
                                            imageName: 'more_vertical',
                                            imagePath: 'images',
                                            color: AppThemeColor.dropDownChildTextColor,
                                            height: 14,
                                            width: 3,
                                          ),
                                          onSelected: (value) {
                                            debugPrint("Selected action: $value");
                                          },
                                          itemBuilder: (context) => [
                                            PopupMenuItem(
                                              value: "AddRequest",
                                              child: CommonText(
                                                text: 'Add Request',
                                                textStyle: mRegularTextStyle16(
                                                  textSize: 12, textColor: AppThemeColor.white
                                                ),
                                              ),
                                              onTap: () {
                                                addRequestDialog(context, collection: collection);
                                              },
                                            ),

                                            PopupMenuItem(
                                              value: "AddFolder",
                                              child: CommonText(
                                                text: 'Add Folder_1',
                                                textStyle: mRegularTextStyle16(
                                                  textSize: 12, textColor: AppThemeColor.white
                                                ),
                                              ),
                                              onTap: () {
                                                addFolderDialog(context, collection: collection);
                                              },
                                            ),

                                            PopupMenuItem(
                                              value: "Rename",
                                              child: CommonText(
                                                text: 'Rename',
                                                textStyle: mRegularTextStyle16(
                                                  textSize: 12,
                                                  textColor: AppThemeColor.white,
                                                ),
                                              ),
                                              onTap: () {
                                                renameCollectionDialog(context, collection);
                                              },
                                            ),

                                            PopupMenuItem(
                                              value: "Delete",
                                              child: CommonText(
                                                text: 'Delete',
                                                textStyle: mRegularTextStyle16(
                                                  textSize: 12, textColor: AppThemeColor.white
                                                ),
                                              ),
                                              onTap: () {
                                                deleteCollection(context, collection);
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              ),
                              // Display child items if expanded
                              if (isExpanded)...[
                                BlocProvider(
                                  create: (context) => CollectionBloc(
                                    repository: CollectionRepository()
                                  )..add(FetchCollectionDetail(
                                    collectionId: collection.collectionId ?? 0,
                                    workspaceId: ApiConstant.cmsWorkspaceId.isNotEmpty ? int.parse(ApiConstant.cmsWorkspaceId) : 1,
                                  )),
                                  child: BlocConsumer<CollectionBloc, CollectionState>(
                                    listener: (context, state) {
                                      if (state is CollectionDetailError) {
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(
                                            content: Text(state.message),
                                            backgroundColor: Colors.red,
                                          ),
                                        );
                                      }
                                    },
                                    builder: (context, state) {
                                      if (state is CollectionDetailLoading) {
                                        return const Padding(
                                          padding: EdgeInsets.only(left: 24.0, top: 8.0),
                                          child: Center(
                                            child: SizedBox(
                                              height: 20,
                                              width: 20,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                              ),
                                            ),
                                          ),
                                        );
                                      }
                                      else if (state is CollectionDetailLoaded && state.collectionDetail != null) {
                                        final collectionDetail = state.collectionDetail!;
                                        return Padding(
                                          padding: const EdgeInsets.only(left: 24.0),
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              if (checkEmpty(collectionDetail.requests) && checkEmpty(collectionDetail.folders))...[
                                                const Padding(
                                                  padding: EdgeInsets.symmetric(vertical: 8.0),
                                                  child: CommonText(
                                                    text: 'No Data Available',
                                                    textStyle: TextStyle(
                                                      color: AppThemeColor.dropDownChildTextColor,
                                                      fontSize: 12,
                                                    ),
                                                  ),
                                                )
                                              ]
                                              else...[
                                                Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    // Display requests in collection
                                                    if (collectionDetail.requests != null && collectionDetail.requests!.isNotEmpty)
                                                      ...collectionDetail.requests!.map((request) =>
                                                        InkWell(
                                                          onTap: () {
                                                            debugPrint('Tapped on request in collection: ${request.toJson()}');
                                                            ApiConstant.requestID = request.id ?? 0;
                                                            context.read<PostmanRequestBloc>().add(
                                                              FetchRequestDetails(
                                                                requestId: request.id ?? 0,
                                                                collectionName: collection.collectionTitle,
                                                                folderName: null,
                                                              ),
                                                            );
                                                          },
                                                          child: Padding(
                                                            padding: const EdgeInsets.symmetric(vertical: 4.0),
                                                            child: Row(
                                                              children: [
                                                                const Icon(
                                                                  Icons.api,
                                                                  size: 16,
                                                                  color: AppThemeColor.dropDownChildTextColor,
                                                                ),
                                                                const SizedBox(width: 8),
                                                                CommonText(
                                                                  text: request.method ?? "GET",
                                                                  textStyle: mRegularTextStyle16(
                                                                    textSize: 12,
                                                                    textColor: AppThemeColor.dropDownChildTextColor,
                                                                  ),
                                                                ),
                                                                const SizedBox(width: 8),
                                                                Expanded(
                                                                  child: CommonText(
                                                                    text: request.name ?? "Unnamed Request",
                                                                    textStyle: mRegularTextStyle16(
                                                                      textSize: 12,
                                                                      textColor: AppThemeColor.dropDownChildTextColor,
                                                                    ),
                                                                    maxLine: 1,
                                                                    overflow: TextOverflow.ellipsis,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ),

                                                    // Display folders
                                                    if (collectionDetail.folders != null && collectionDetail.folders!.isNotEmpty)
                                                      ...collectionDetail.folders!.map((folder) {
                                                        if (folder.name == null || folder.name!.isEmpty) {
                                                          debugPrint(' Root folder name is null or empty');
                                                          folder.name = "Folder ${folder.id}";
                                                        }

                                                        // Process nested folders recursively
                                                        if (folder.folders != null && folder.folders!.isNotEmpty) {
                                                          debugPrint('Root folder ${folder.name} has ${folder.folders!.length} subfolders');

                                                          for (var subfolder in folder.folders!) {
                                                            debugPrint('Processing subfolder: id=${subfolder.id}, name=${subfolder.name}, parentId=${subfolder.parentFolderId}');

                                                            if (subfolder.name == null || subfolder.name!.isEmpty) {
                                                              debugPrint(' Nested folder name is null or empty, using fallback name');
                                                              subfolder.name = "Subfolder ${subfolder.id}";
                                                            }

                                                            // Check if this subfolder has its own nested folders
                                                            if (subfolder.folders != null && subfolder.folders!.isNotEmpty) {
                                                              debugPrint('Subfolder ${subfolder.name} has ${subfolder.folders!.length} nested folders');

                                                              for (var nestedFolder in subfolder.folders!) {
                                                                debugPrint('Nested subfolder: id=${nestedFolder.id}, name=${nestedFolder.name}, parentId=${nestedFolder.parentFolderId}');

                                                                if (nestedFolder.name == null || nestedFolder.name!.isEmpty) {
                                                                  debugPrint(' Nested subfolder name is null or empty');
                                                                  nestedFolder.name = "Nested Folder ${nestedFolder.id}";
                                                                }
                                                              }
                                                            }
                                                          }
                                                        }

                                                        return _buildFolderItem(folder);
                                                      }),
                                                  ],
                                                ),
                                              ]
                                            ],
                                          ),
                                        );
                                      }
                                      else if (state is CollectionDetailError) {
                                        return Padding(
                                          padding: const EdgeInsets.only(left: 24.0, top: 8.0),

                                          child: CommonText(
                                            text:  'Error: ${state.message}',
                                            textStyle: mRegularTextStyle16(
                                              textColor: Colors.red,
                                              textSize: 12,
                                            ),
                                          )
                                        );
                                      }
                                      else {
                                        return Padding(
                                          padding: const EdgeInsets.only(left: 24.0, top: 8.0),
                                          child: CommonText(
                                            text: 'No data available',
                                            textStyle: mRegularTextStyle16(
                                              textColor: AppThemeColor.dropDownChildTextColor,
                                              textSize: 12,
                                            ),
                                          ),
                                        );
                                      }
                                    },
                                  ),
                                )
                              ],
                            ],
                          );
                        },
                      );
                    }
                    else if(state is CollectionError){
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(state.message),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return const Center(child: Text('Error loading collections'));
                    }
                    return const Center(child: Text('Something went wrong'));
                  },
                ),
              )
            ],
          ),
        );
      },
    )
    );
  }

  void _processNestedFolders(Folder folder) {
    if (folder.folders == null || folder.folders!.isEmpty) {
      return;
    }

    for (var subfolder in folder.folders!) {
      debugPrint('Processing subfolder: id=${subfolder.id}, name=${subfolder.name}, parentId=${subfolder.parentFolderId}');

      // Ensure subfolder name is not null or empty
      if (subfolder.name == null || subfolder.name!.isEmpty) {
        debugPrint('WARNING: Subfolder name is null or empty, using fallback name');
        subfolder.name = "Subfolder ${subfolder.id}";
      }

      // Process nested folders recursively
      if (subfolder.folders != null && subfolder.folders!.isNotEmpty) {
        debugPrint('Subfolder ${subfolder.name} has ${subfolder.folders!.length} nested folders');
        _processNestedFolders(subfolder);
      }
    }
  }

  // Build a folder item with its children
  Widget _buildFolderItem(Folder folder) {
    debugPrint('Building folder item: id=${folder.id}, name=${folder.name}, parentId=${folder.parentFolderId}');

    debugPrint('FOLDER DETAILS: ${folder.toJson()}');

    if (folder.name == null || folder.name!.isEmpty) {
      debugPrint('WARNING: Folder name is null or empty in _buildFolderItem, using fallback name');
      folder.name = "Folder ${folder.id}";
    }

    if (folder.folders != null && folder.folders!.isNotEmpty) {
      debugPrint('Folder has ${folder.folders!.length} subfolders');
      _processNestedFolders(folder);
    }

    return StatefulBuilder(
      builder: (context, setState) {
        final isExpanded = expandedFolders[folder.id ?? 0] ?? false;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWell(
              onTap: () {
                setState(() {
                  expandedFolders[folder.id ?? 0] = !isExpanded;
                });
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        CommonSVGIcon(
                          imageName: isExpanded ? 'direction_down' : 'direction_right',
                          imagePath: 'images',
                          color: AppThemeColor.dropDownChildTextColor,
                          height: 20,
                          width: 20,
                        ),
                        const SizedBox(width: 4),
                        const CommonSVGIcon(
                          imageName: 'folder2',
                          imagePath: 'images',
                          color: AppThemeColor.dropDownChildTextColor,
                          height: 20,
                          width: 20,
                        ),
                        const SizedBox(width: 8),
                        CommonText(
                          text: (folder.name != null && folder.name!.isNotEmpty)
                              ? folder.name!
                              : "Folder ${folder.id}",
                          textStyle: mRegularTextStyle16(
                            textSize: 12,
                            textColor: AppThemeColor.dropDownChildTextColor,
                          ),
                          maxLine: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 24,
                      child: PopupMenuButton<String>(
                        padding: EdgeInsets.zero,
                        clipBehavior: Clip.none,
                        color: AppThemeColor.lightBackground,
                        icon: const CommonSVGIcon(
                          imageName: 'more_vertical',
                          imagePath: 'images',
                          color: AppThemeColor.dropDownChildTextColor,
                          height: 14,
                          width: 3,
                        ),
                        onSelected: (value) {
                          debugPrint("Selected action: $value");
                        },
                        itemBuilder: (context) => [
                          PopupMenuItem(
                            value: "AddRequest",
                            child: CommonText(
                              text: 'Add Request',
                              textStyle: mRegularTextStyle16(
                                textSize: 12, textColor: AppThemeColor.white
                              ),
                            ),
                            onTap: () {
                              addRequestDialog(context, folder: folder);
                            },
                          ),
                          PopupMenuItem(
                            value: "AddFolder",
                            child: CommonText(
                              text: 'Add Folder_2',
                              textStyle: mRegularTextStyle16(
                                textSize: 12, textColor: AppThemeColor.white
                              ),
                            ),
                            onTap: () {
                              addFolderDialog(context, parentFolder: folder);
                            },
                          ),
                          PopupMenuItem(
                            value: "Rename",
                            child: CommonText(
                              text: 'Rename',
                              textStyle: mRegularTextStyle16(
                                textSize: 12,
                                textColor: AppThemeColor.white,
                              ),
                            ),
                            onTap: () {
                              renameFolder(context, folder);
                            },
                          ),
                          PopupMenuItem(
                            value: "Delete",
                            child: CommonText(
                              text: 'Delete',
                              textStyle: mRegularTextStyle16(
                                textSize: 12,
                                textColor: AppThemeColor.white,
                              ),
                            ),
                            onTap: () {
                              deleteFolderDialog(context, folder);
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (isExpanded)
              Padding(
                padding: const EdgeInsets.only(left: 20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if(isCheckEmpty(folder.requests) && isCheckEmpty(folder.folders))...[
                      const Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0),
                        child: CommonText(
                          text: 'No Data Available',
                          textStyle: TextStyle(
                            color: AppThemeColor.dropDownChildTextColor,
                            fontSize: 12,
                          ),
                        ),
                      )
                    ]
                    else...[
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Display requests in this folder
                          if (folder.requests != null && folder.requests!.isNotEmpty)
                            ...folder.requests!.map((request) =>
                              InkWell(
                                onTap: () {
                                  debugPrint('Tapped on request in folder: ${request.toJson()}');
                                  ApiConstant.requestID = request.id ?? 0;
                                  context.read<PostmanRequestBloc>().add(
                                    FetchRequestDetails(
                                      requestId: request.id ?? 0,
                                      collectionName: null,
                                      folderName: folder.name,
                                    ),
                                  );
                                },
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                                  child: Row(
                                    children: [
                                      const Icon(
                                        Icons.api,
                                        size: 16,
                                        color: AppThemeColor.dropDownChildTextColor,
                                      ),
                                      const SizedBox(width: 8),
                                      CommonText(
                                        text: request.method ?? "GET",
                                        textStyle: mRegularTextStyle16(
                                          textSize: 12,
                                          textColor: AppThemeColor.dropDownChildTextColor,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: CommonText(
                                          text: request.name ?? "Unnamed",
                                          textStyle: mRegularTextStyle16(
                                            textSize: 12,
                                            textColor: AppThemeColor.dropDownChildTextColor,
                                          ),
                                          maxLine: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),

                          // Display subfolders
                          if (folder.folders != null && folder.folders!.isNotEmpty)
                            ...folder.folders!.map((subfolder) {
                              debugPrint('Rendering subfolder: id=${subfolder.id}, name=${subfolder.name}, parentId=${subfolder.parentFolderId}');

                              if (subfolder.name == null || subfolder.name!.isEmpty) {
                                debugPrint('WARNING: Subfolder name is null or empty, using fallback name');
                                subfolder.name = "Subfolder ${subfolder.id}";
                              }

                              if (subfolder.folders != null && subfolder.folders!.isNotEmpty) {
                                debugPrint('Subfolder has ${subfolder.folders!.length} nested folders');

                                // Ensure all nested folders have valid names
                                for (var nestedFolder in subfolder.folders!) {
                                  if (nestedFolder.name == null || nestedFolder.name!.isEmpty) {
                                    debugPrint('WARNING: Nested subfolder name is null or empty, using fallback name');
                                    nestedFolder.name = "Nested Folder ${nestedFolder.id}";
                                  }
                                }
                              }

                              // Auto-expand the folder if it has subfolders
                              if (subfolder.folders != null && subfolder.folders!.isNotEmpty) {
                                expandedFolders[subfolder.id!] = true;
                              }

                              return Padding(
                                padding: const EdgeInsets.only(left: 8.0),
                                child: _buildFolderItem(subfolder),
                              );
                            }),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
          ],
        );
      },
    );
  }


  void _showRenameRequestDialog(BuildContext context, String requestId,
      String currentName, String? folderName, String collectionName) {
    final requestNameController = TextEditingController(text: currentName);

    CommonAlertDialog.show(
      context: context,
      buttonCreateText: "Rename",
      title: 'Rename Request',
      description: 'Enter a new name for the request.',
      inputFields: [
        CommonTextFormField(
          labelText: "Request Name",
          hintTextString: "Enter Request Name",
          isLabelText: true,
          isLabelTextBold: true,
          fontSize: 14,
          hintStyle: TextStyle(color: AppThemeColor.hintTextColor),
          labelTextSize: Globals.labelTextSizeForDialog,
          textEditController: requestNameController,
          cornerRadius: 4,
          errorMessage: "",
        ),
      ],
      onCancel: () {
        Navigator.of(context).pop();
      },
      onCreate: () {
        final newName = requestNameController.text;
        if (newName.isNotEmpty) {
          homeBloc.add(
            RenameRequestEvent(
              requestId: requestId,
              newName: newName,
              collectionName: collectionName,
              folderName: folderName, // Pass folderName (can be null)
            ),
          );
        }
        Navigator.of(context).pop();
      },
    );
  }

  void renameFolder(BuildContext context, Folder folder){
    final folderNameController = TextEditingController(text: folder.name);
    final descriptionController = TextEditingController(text: folder.description);

    CommonAlertDialog.show(
      context: context,
      buttonCreateText: "Rename",
      title: 'Rename Folder',
      description: 'Enter a new name for the folder.',
      inputFields: [
        CommonTextFormField(
          labelText: "Folder Name",
          hintTextString: "Enter Folder Name",
          isLabelText: true,
          isLabelTextBold: true,
          fontSize: 14,
          hintStyle: const TextStyle(color: AppThemeColor.hintTextColor),
          labelTextSize: Globals.labelTextSizeForDialog,
          textEditController: folderNameController,
          cornerRadius: 4,
          errorMessage: "",
        ),
        const SizedBox(height: 16),
        // CommonTextFormField(
        //   labelText: "Description (Optional)",
        //   hintTextString: "Enter Folder Description",
        //   isLabelText: true,
        //   isLabelTextBold: true,
        //   fontSize: 14,
        //   hintStyle: const TextStyle(color: AppThemeColor.hintTextColor),
        //   labelTextSize: Globals.labelTextSizeForDialog,
        //   textEditController: descriptionController,
        //   cornerRadius: 4,
        //   errorMessage: "",
        // ),
      ],
      onCancel: () {
        Navigator.of(context).pop();
      },
      onCreate: () {
        final newName = folderNameController.text;
        final description = descriptionController.text;

        if (newName.isNotEmpty) {
          try {
            // Get the FolderBloc instance
            final folderBloc = BlocProvider.of<FolderBloc>(context, listen: false);

            // Dispatch the UpdateFolder event
            folderBloc.add(
              UpdateFolder(
                folderId: folder.id ?? 0,
                name: newName,
                description: description,
                collectionId: folder.collectionId.toString(),
                parentFolderId: folder.parentFolderId?.toString(),
              ),
            );

            // Show a loading indicator
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Updating folder...'),
                duration: Duration(seconds: 1),
              ),
            );
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error: ${e.toString()}')),
            );
          }
        }
        Navigator.of(context).pop();
      },
    );
  }

  void renameCollectionDialog(BuildContext context, Collection collection) {
    final collectionNameController = TextEditingController(text: collection.collectionTitle ?? '');
    final descriptionController = TextEditingController(text: collection.collectionDescription ?? '');

    CommonAlertDialog.show(
      context: context,
      buttonCreateText: "Rename",
      title: 'Rename Collection',
      description: 'Enter a new name for the collection.',
      inputFields: [
        CommonTextFormField(
          labelText: "Collection Name",
          hintTextString: "Enter Collection Name",
          isLabelText: true,
          isLabelTextBold: true,
          fontSize: 14,
          hintStyle: const TextStyle(color: AppThemeColor.hintTextColor),
          labelTextSize: Globals.labelTextSizeForDialog,
          textEditController: collectionNameController,
          cornerRadius: 4,
          errorMessage: "",
        ),
        const SizedBox(height: 16),
        // CommonTextFormField(
        //   labelText: "Description (Optional)",
        //   hintTextString: "Enter Collection Description",
        //   isLabelText: true,
        //   isLabelTextBold: true,
        //   fontSize: 14,
        //   hintStyle: const TextStyle(color: AppThemeColor.hintTextColor),
        //   labelTextSize: Globals.labelTextSizeForDialog,
        //   textEditController: descriptionController,
        //   cornerRadius: 4,
        //   errorMessage: "",
        // ),
      ],
      onCancel: () {
        Navigator.of(context).pop();
      },
      onCreate: () {
        final newName = collectionNameController.text;
        final description = descriptionController.text;

        if (newName.isNotEmpty) {
          try {
            // Get the CollectionBloc instance
            final collectionBloc = BlocProvider.of<CollectionBloc>(context, listen: false);

            // Dispatch the UpdateCollection event
            collectionBloc.add(
              UpdateCollection(
                collectionId: collection.collectionId ?? 0,
                title: newName,
                description: description,
                workspaceId: int.parse(ApiConstant.cmsWorkspaceId),
              ),
            );

            // Show a loading indicator
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Updating collection...'),
                duration: Duration(seconds: 1),
              ),
            );
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error: ${e.toString()}')),
            );
          }
        }
        Navigator.of(context).pop();
      },
    );
  }

  void deleteFolderDialog(BuildContext context, Folder folder) {
    // Store the collection and parent folder IDs for later use
    lastCollectionId = folder.collectionId;
    lastParentFolderId = folder.parentFolderId;

    CommonDeleteAlertDialog.show(
      context: context,
      buttonCreateText: "Delete",
      title: 'Delete Folder?',
      description: 'Are you sure you want to permanently delete this folder? This action cannot be undone.',
      inputFields: [],
      onCancel: () {
        Navigator.of(context).pop();
      },
      onCreate: () {
        try {
          final folderBloc = BlocProvider.of<FolderBloc>(context, listen: false);
          if (!isCheckEmpty(folder.id)) {
            debugPrint('Deleting folder with ID: ${folder.id}');
            folderBloc.add(
              DeleteFolder(
                folderId: folder.id!,
              ),
            );

            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Deleting folder...'),
                duration: Duration(seconds: 1),
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Error: Folder ID is null'),
              ),
            );
          }
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: ${e.toString()}')),
          );
        }

        Navigator.of(context).pop();
      },
    );
  }

  void deleteCollection(BuildContext context, Collection collection) {
    CommonDeleteAlertDialog.show(
      context: context,
      buttonCreateText: "Delete",
      title: 'Delete Collection?',
      description:'Are you sure you want to permanently delete this collection? This action cannot be undone.',
      inputFields: [],
      onCancel: () {
        Navigator.of(context).pop();
      },
      onCreate: () {
        try {
          // Get the CollectionBloc instance
          final collectionBloc = BlocProvider.of<CollectionBloc>(context, listen: false);

          // Dispatch the DeleteCollection event
          collectionBloc.add(
            DeleteCollection(
              collectionId: collection.collectionId ?? 0,
            ),
          );

          // Show a loading indicator
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Deleting collection...'),
              duration: Duration(seconds: 1),
            ),
          );
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: ${e.toString()}')),
          );
        }
        Navigator.of(context).pop();
      },
    );
  }

  void addRequestDialog(BuildContext context, { Collection? collection, Folder? folder}){
    final requestNameController = TextEditingController();
    lastCollectionId = collection?.collectionId;
    lastParentFolderId = folder?.id;

    CommonAlertDialog.show(
      context: context,
      buttonCreateText: "Create",
      title: 'Add Request',
      description: 'Create a new request.',
      inputFields: [
        CommonTextFormField(
          labelText: "Enter Request Name",
          hintTextString: "Enter Request Name",
          isLabelText: true,
          isLabelTextBold: true,
          fontSize: 14,
          hintStyle: const TextStyle(color: AppThemeColor.hintTextColor),
          labelTextSize: Globals.labelTextSizeForDialog,
          textEditController: requestNameController,
          cornerRadius: 4,
          errorMessage: "",
        ),
      ],
      onCancel: () {
        Navigator.of(context).pop();
      },
      onCreate: () {
        final requestName = requestNameController.text;

        if (!isCheckEmpty(requestName)) {
          try {
            final requestBloc = BlocProvider.of<PostmanRequestBloc>(context, listen: false);
            if (folder != null && folder.id != null) {
              debugPrint('Creating request in folder: ${folder.id}');
              requestBloc.add(
                CreatePostmanRequest(
                  name: requestName,
                  url: '',
                  method: 'GET',
                  folderId: folder.id.toString(),
                  workspaceId: int.parse(ApiConstant.cmsWorkspaceId),
                )
              );
            } else if (collection != null && collection.collectionId != null) {
              debugPrint('Creating request in collection: ${collection.collectionId}');
              requestBloc.add(
                CreatePostmanRequest(
                  name: requestName,
                  url: '',
                  method: 'GET',
                  collectionId: collection.collectionId.toString(),
                  workspaceId: int.parse(ApiConstant.cmsWorkspaceId),
                )
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Error: No collection or folder selected'),
                  backgroundColor: Colors.red,
                ),
              );
              return;
            }
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Creating request...'),
                duration: Duration(seconds: 1),
              ),
            );
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error: ${e.toString()}')),
            );
          }
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please enter a request name'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }

        Navigator.of(context).pop();
      },
    );
  }


  void _showAddCollectionDialog(BuildContext context) {
    final addCollectionController = TextEditingController();
    final descriptionController = TextEditingController();

    CommonAlertDialog.show(
      context: context,
      buttonCreateText: "Create",
      title: 'New Collection',
      description: 'Create a new collection to manage your API requests.',
      inputFields: [
        CommonTextFormField(
          labelText: "Enter Collection Name",
          hintTextString: "Enter Collection Name",
          isLabelText: true,
          isLabelTextBold: true,
          isValidationRequired: true,
          fontSize: 14,
          hintStyle: const TextStyle(color: AppThemeColor.hintTextColor),
          labelTextSize: Globals.labelTextSizeForDialog,
          textEditController: addCollectionController,
          cornerRadius: 4,
          errorMessage: "This field cannot be empty",
        ),
        const SizedBox(height: 16),
        // CommonTextFormField(
        //   labelText: "Description (Optional)",
        //   hintTextString: "Enter Collection Description",
        //   isLabelText: true,
        //   isLabelTextBold: true,
        //   fontSize: 14,
        //   hintStyle: const TextStyle(color: AppThemeColor.hintTextColor),
        //   labelTextSize: Globals.labelTextSizeForDialog,
        //   textEditController: descriptionController,
        //   cornerRadius: 4,
        //   errorMessage: "",
        // ),
      ],
      onCancel: () {
        Navigator.of(context).pop();
      },
      onCreate: () {
        final collectionName = addCollectionController.text;
        final description = !isCheckEmpty(descriptionController.text)
            ? descriptionController.text
            : "Collection created on ${DateTime.now().toString().split('.')[0]}";

        if (!isCheckEmpty(collectionName)) {
          try {
            final collectionBloc = BlocProvider.of<CollectionBloc>(context);
            collectionBloc.add(
              CreateCollection(
                title: collectionName,
                description: description,
                workspaceId: int.parse(ApiConstant.cmsWorkspaceId),
              )
            );
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Creating collection...'),
                duration: Duration(seconds: 1),
              ),
            );
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error: ${e.toString()}')),
            );
          }
        }
        Navigator.of(context).pop();
      },
    );
  }

  void addFolderDialog(BuildContext context,{ Collection? collection, Folder? parentFolder}) {
    final folderNameController = TextEditingController();
    final descriptionController = TextEditingController();

    // Store the collection and parent folder IDs for later use
    lastCollectionId = collection?.collectionId;
    lastParentFolderId = parentFolder?.id;

    CommonAlertDialog.show(
      context: context,
      buttonCreateText: "Create",
      title: 'Add Folder',
      description: 'Create a new folder to organize your API requests.',
      inputFields: [
        CommonTextFormField(
          labelText: "Enter Folder Name",
          hintTextString: "Enter Folder Name",
          isLabelText: true,
          isLabelTextBold: true,
          isValidationRequired: true,
          fontSize: 14,
          hintStyle: const TextStyle(color: AppThemeColor.hintTextColor),
          labelTextSize: Globals.labelTextSizeForDialog,
          textEditController: folderNameController,
          cornerRadius: 4,
          errorMessage: "This field cannot be empty",
        ),
        const SizedBox(height: 16),
        // CommonTextFormField(
        //   labelText: "Description (Optional)",
        //   hintTextString: "Enter Folder Description",
        //   isLabelText: true,
        //   isLabelTextBold: true,
        //   fontSize: 14,
        //   hintStyle: const TextStyle(color: AppThemeColor.hintTextColor),
        //   labelTextSize: Globals.labelTextSizeForDialog,
        //   textEditController: descriptionController,
        //   cornerRadius: 4,
        //   errorMessage: "",
        // ),
      ],
      onCancel: () {
        Navigator.of(context).pop();
      },
      onCreate: () {
        final folderName = folderNameController.text;
        final description = !isCheckEmpty(descriptionController.text)
            ? descriptionController.text
            : "Folder created on ${DateTime.now().toString().split('.')[0]}";

        if (!isCheckEmpty(folderName)) {
          try {
            // Create a FolderBloc instance
            final folderBloc = BlocProvider.of<FolderBloc>(context, listen: false);

            // Determine if we're creating a folder in a collection or a subfolder
            if (parentFolder != null) {
              // Creating a subfolder
              folderBloc.add(
                CreateFolder(
                  name: folderName,
                  description: description,
                  parentFolderId: parentFolder.id?.toString(),
                )
              );
            } else {
              // Creating a folder in a collection
              folderBloc.add(
                CreateFolder(
                name: folderName,
                description: description,
                collectionId: collection?.collectionId.toString(),
                )
              );
            }

            // Show a loading indicator
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Creating folder...'),
                duration: Duration(seconds: 1),
              ),
            );
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error: ${e.toString()}')),
            );
          }
        }
        Navigator.of(context).pop();
      },
    );
  }

  void _showAddFolderDialog(BuildContext context, String collectionName,
      [String? parentFolderName]) {
    final folderNameController = TextEditingController();

    CommonAlertDialog.show(
      context: context,
      buttonCreateText: "Create",
      title: 'Add Folder',
      description: 'Create a new folder to organize your API requests.',
      inputFields: [
        CommonTextFormField(
          labelText: "Enter Folder Name",
          hintTextString: "Enter Folder Name",
          isLabelText: true,
          isLabelTextBold: true,
          fontSize: 14,
          hintStyle: TextStyle(color: AppThemeColor.hintTextColor),
          labelTextSize: Globals.labelTextSizeForDialog,
          textEditController: folderNameController,
          cornerRadius: 4,
          errorMessage: "",
        ),
      ],
      onCancel: () {
        Navigator.of(context).pop();
      },
      onCreate: () {
        final folderName = folderNameController.text;
        if (folderName.isNotEmpty) {
          try {
            homeBloc.add(
              AddFolderEvent(
                collectionName,
                folderName,
                parentFolderName: parentFolderName,
              ),
            );
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(e.toString())),
            );
          }
        }
        Navigator.of(context).pop();
      },
    );
  }
}
