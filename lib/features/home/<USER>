import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive/hive.dart';

import '../../globals.dart';
import '../../helpers/color_config.dart';
import '../../helpers/style_config.dart';
import '../common_widgets/CommonAlertDialog.dart';
import '../common_widgets/CommonSVGIcon.dart';
import '../common_widgets/CommonText.dart';
import '../common_widgets/CommonTextButton.dart';
import '../common_widgets/CommonTextFormField.dart';
import 'bloc/home_bloc.dart';
import 'bloc/home_event.dart';
import 'bloc/home_state.dart';
import 'data/api_collection.dart';

class HistoryPanel extends StatefulWidget {
  @override
  State<HistoryPanel> createState() => _HistoryPanelState();
}

class _HistoryPanelState extends State<HistoryPanel> {
  late HomeBloc homeBloc;
  final searchController = TextEditingController();
  @override
  void initState() {
    super.initState();
    homeBloc = BlocProvider.of<HomeBloc>(context,listen: false);
    WidgetsBinding.instance.addPostFrameCallback((_){
      homeBloc.add(LoadRequestHistoryEvent());
    });
    printRequestHistory();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        final history = state.requestHistory;

        bool isHistoryEmpty = history.isNotEmpty;

        final filteredHistory = _filterHistory(history, state.searchQuery);

        // Group requests by date
        final groupedHistory = _groupRequestsByDate(filteredHistory);

        return Padding(
          padding: const EdgeInsets.only(left: 10.0),
          child: Column(
            children: [

              SizedBox(height: 10,),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [

                  CommonText(
                    text: 'History',
                    textStyle: mRegularTextStyle16(
                        textSize: 12, textColor: AppThemeColor.white),
                  ),

                  Row(
                    children: [
                      CommonTextButton(
                        text: 'Clear all',
                        enabled: isHistoryEmpty,
                        textStyle: mRegularTextStyle16(
                            textSize: 12, textColor: AppThemeColor.lightBlue),
                        callback: (){
                          _showClearHistory(context);
                        },
                      ),
                    ],
                  )
                ],
              ),

              SizedBox(height: 5,),

              // Search Input Field
              //if(state.requestHistory.length -1 > 0)
              if(state.requestHistory.isNotEmpty)
              CommonTextFormField(
                hintTextString: "Search",
                isLabelText: false,
                fontSize: 14,
                padding: EdgeInsets.only(right: 0, left: 0, top: 10, bottom: 10),
                hintStyle: TextStyle(color: AppThemeColor.hintTextColor),
                textEditController: searchController,
                cornerRadius: Globals.defaultTextInputCornerRadius,
                isDense: true,
                errorMessage: "",
                onChange: (value) {
                  homeBloc.add(SearchQueryChangedEvent(value));
                },
              ),

              const SizedBox(height: 10),

              // ListView with Grouped History
              Expanded(
                child: ListView.builder(
                  itemCount: groupedHistory.length,
                  padding: EdgeInsets.zero,
                  itemBuilder: (context, index) {
                    final entry = groupedHistory.entries.elementAt(index);
                    final date = entry.key;
                    final requests = entry.value;

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CommonText(
                          text: date,
                          textStyle: mMediumTextStyle16(
                            textSize: 14,
                            textColor: AppThemeColor.white,
                          ),
                        ),
                        ...requests.map((request) => _buildRequestItem(request)).toList(),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void printRequestHistory() async {
    final box = await Hive.openBox('requestHistoryBox');
    print('boxHistory: ${box.values.toList()}');
  }

  List<RequestHistoryModel> _filterHistory(List<RequestHistoryModel> history, String query) {
    if (query.isEmpty) return history;

    return history.where((request) =>
    request.requestName.toLowerCase().contains(query.toLowerCase()) ||
        request.method.toLowerCase().contains(query.toLowerCase()) ||
        request.url.toLowerCase().contains(query.toLowerCase()),
    ).toList();
  }

  Map<String, List<RequestHistoryModel>> _groupRequestsByDate(List<RequestHistoryModel> history) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(Duration(days: 1));

    final groupedHistory = <String, List<RequestHistoryModel>>{};

    for (final request in history) {
      final requestDate = DateTime(request.timestamp.year, request.timestamp.month, request.timestamp.day);
      String dateLabel;

      if (requestDate == today) {
        dateLabel = 'Today';
      } else if (requestDate == yesterday) {
        dateLabel = 'Yesterday';
      } else {
        dateLabel = '${_getMonthName(requestDate.month)} ${requestDate.day}, ${requestDate.year}';
      }

      groupedHistory.putIfAbsent(dateLabel, () => []).add(request);
    }

    return groupedHistory;
  }

  String _getMonthName(int month) {
    switch (month) {
      case 1:
        return 'January';
      case 2:
        return 'February';
      case 3:
        return 'March';
      case 4:
        return 'April';
      case 5:
        return 'May';
      case 6:
        return 'June';
      case 7:
        return 'July';
      case 8:
        return 'August';
      case 9:
        return 'September';
      case 10:
        return 'October';
      case 11:
        return 'November';
      case 12:
        return 'December';
      default:
        return '';
    }
  }

  Widget _buildRequestItem(RequestHistoryModel request) {
    return Row(
      //mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [

        SizedBox(width: 10,),

        Expanded(
          child: InkWell(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 5,),
                CommonText(
                  text: request.requestName,
                  textStyle: mRegularTextStyle16(
                      textSize: 12, textColor: AppThemeColor.white),
                ),

                CommonText(
                  text: '${request.method} - ${request.url}',
                  //maxLength: 40,
                  textStyle: mRegularTextStyle16(
                      textSize: 12, textColor: AppThemeColor.tabUnselectedTextColor),
                ),
              ],
            ),
            onTap: (){
              homeBloc.add(
                OpenTabEvent(
                  tabModel: TabModel(
                    uuid: request.id,
                    tabName: request.requestName,
                    method: request.method,
                    url: request.url,
                    jsonData: request.jsonData,
                  ),
                ),
              );
            },
          ),
        ),

        IconButton(
          icon: Icon(
            Icons.delete,
            size: 20,
            color: AppThemeColor.tabUnselectedTextColor,
          ),
          onPressed: () {
            // Delete the request from history
            homeBloc.add(DeleteRequestFromHistoryEvent(request.id));
          },
        ),

      ],
    );


      /*ListTile(
      title: CommonText(
        text: request.requestName,
        textStyle: mRegularTextStyle16(
            textSize: 14, textColor: AppThemeColor.white),
      ),
      subtitle: CommonText(
        text: '${request.method} - ${request.url}',
        textStyle: mRegularTextStyle16(
            textSize: 12, textColor: AppThemeColor.tabUnselectedTextColor),
      ),

      onTap: () {
        // Reload the request into the editor
        context.read<HomeBloc>().add(
          OpenTabEvent(
            tabModel: TabModel(
              uuid: request.id,
              tabName: request.requestName,
              method: request.method,
              url: request.url,
              jsonData: request.jsonData,
            ),
          ),
        );
      },
      trailing: IconButton(
        icon: Icon(Icons.delete),
        onPressed: () {
          // Delete the request from history
          context.read<HomeBloc>().add(DeleteRequestFromHistoryEvent(request.id));
        },
      ),
    );*/

  }

  void _showClearHistory(BuildContext context) {
    CommonDeleteAlertDialog.show(
      context: context,
      buttonCreateText: "Clear",
      title: 'Clear request history?',
      description: 'Are you sure you want to clear request history? This action cannot be undone.',
      inputFields: [],
      onCancel: () {
        Navigator.of(context).pop();
      },
      onCreate: () {
        try {
          context.read<HomeBloc>().add(ClearRequestHistoryEvent());
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(e.toString())),
          );
        }
        Navigator.of(context).pop();
      },
    );
  }
}