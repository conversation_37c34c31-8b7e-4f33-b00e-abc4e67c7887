import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_code_editor/flutter_code_editor.dart';
import 'package:postman_flutter/features/home/<USER>/home_state.dart';
import 'package:postman_flutter/features/home/<USER>';
import 'package:postman_flutter/features/home/<USER>/send_request/request_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/send_request/request_event.dart';
import '../../../../helpers/color_config.dart';
import '../../../../helpers/style_config.dart';
import '../../../common_widgets/CommonButton.dart';
import '../../../common_widgets/common_dropdown.dart';
import 'package:http/http.dart' as http;
import '../../../common_widgets/dynamic_radio_tabs/bloc/tab_bloc.dart';
import '../../../common_widgets/dynamic_radio_tabs/bloc/tab_state.dart';
import '../../bloc/home_bloc.dart';
import '../../bloc/home_event.dart';
import '../authorization/basic_auth/basic_auth.dart';
import '../authorization/bloc/auth_subviews_bloc.dart';
import '../authorization/bloc/auth_subviews_state.dart';
import '../authorization/bloc/authorization_bloc.dart';
import '../authorization/bloc/authorization_state.dart';
import '../body/form_data/bloc/form_data_bloc.dart';
import '../body/form_data/bloc/form_data_model.dart';
import '../body/json_data/bloc/json_data_bloc.dart';
import '../header/bloc/headers_bloc.dart';
import '../header/bloc/headers_model.dart';
import '../query_params/bloc/query_params_bloc.dart';
import '../query_params/bloc/query_params_event.dart';
import '../query_params/bloc/query_params_state.dart';
import 'request_bloc.dart'; // Import RequestBloc-related code


/*class RequestSenderView extends StatefulWidget {
  final TextEditingController urlController;
  final String method;
  final String uuid;

  const RequestSenderView({
    required this.urlController,
    required this.method,
    required this.uuid,
    Key? key,
  }) : super(key: key);

  @override
  State<RequestSenderView> createState() => _RequestSenderViewState();
}

class _RequestSenderViewState extends State<RequestSenderView> {
  // Controllers for the URL field and default method
  //final TextEditingController urlController = TextEditingController();
  String selectedMethod = "GET";



  @override
  void initState() {

    super.initState();

   // widget.url.text = urlController.text; // Initialize controller with the passed value
    //selectedMethod = widget.method;

    context.read<HomeBloc>().stream.listen((state) {
      if (state.activeTab != null) {
        widget.urlController.text = state.activeTab!.url ?? '';
        selectedMethod = state.activeTab!.method;
      }
    });
  }


  @override
  Widget build(BuildContext context) {



    return MultiBlocListener(
      listeners: [

        BlocListener<AuthSubViewsBloc, AuthSubViewsState>(
          // listenWhen: (previous, current) =>
          // previous.username != current.username ||
          //     previous.password != current.password,
          listener: (context, state) {
            //getting data from basic auth
            if (state.authType == "Basic Auth") {
              // Optionally show feedback or log the updated values
              debugPrint("UsernameRS: ${state.basicAuthFields.username}");
              debugPrint("PasswordRS: ${state.basicAuthFields.password}");
            }

            //getting data from bearer token
            if (state.authType == "Bearer Token") {
              // Optionally show feedback or log the updated values
              debugPrint("TokenRS: ${state.bearerTokenFields.token}");
            }

            debugPrint("AuthType: ${state.authType}");

            debugPrint("HeaderOrQueryParamsApiKey: ${state.apiKeyFields.addToHeaderOrQueryParams}");

            //getting data from api key
            if (state.authType == "API Key") {
              // Optionally show feedback or log the updated values
              debugPrint("ApiKey: ${state.apiKeyFields.key}");
              debugPrint("ApiValue: ${state.apiKeyFields.value}");
              debugPrint("ApiHeaderOrQueryParams: ${state.apiKeyFields.addToHeaderOrQueryParams}");
            }
            //getting data from jwt bearer
            debugPrint("algorithm: ${state.jwtBearerFields.algorithm}");
            debugPrint("secretToken: ${state.jwtBearerFields.secretToken}");
            debugPrint("payload: ${state.jwtBearerFields.payload}");
            debugPrint("requestHeaderprefix: ${state.jwtBearerFields.requestHeaderprefix}");
            debugPrint("jwtHeader: ${state.jwtBearerFields.jwtHeader}");

            debugPrint("jwtTokenToHeaderOrQueryParams: ${state.jwtBearerFields.jwtTokenToHeaderOrQueryParams}");

            debugPrint('AuthSubViewsBloc Listener');
          },
        ),

        BlocListener<QueryParamsBloc, QueryParamsState>(
          listener: (context, state) {
            final baseUrl = widget.urlController.text.split('?').first;
            final queryString = state.generateQueryString();
            final newUrl = queryString.isNotEmpty ? '$baseUrl$queryString' : baseUrl;

            if (widget.urlController.text != newUrl) {
              widget.urlController.text = newUrl;
            }
            debugPrint('QueryParams Listener:');
          },
        ),
      ],
      child: BlocBuilder<TabBloc, TabState>(
        builder: (context, state) {
          final selectedTabIndex = state is TabUpdated ? state.tabIndex : 0;
          return  BlocBuilder<AuthSubViewsBloc, AuthSubViewsState>(
            builder: (context, state) {
              print( "Selected Auth Type: ${state.authType}");
              return Container(
                padding: EdgeInsets.only(left: 20, right: 20),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Container(
                        height: 40,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: AppThemeColor.commonBorderColor,
                            width: 1.0,
                          ),
                          borderRadius: BorderRadius.circular(4.0),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            const SizedBox(width: 8.0),
                            BlocBuilder<HomeBloc, HomeState>(
                             builder: (context, state) {
                               return SizedBox(
                              width: 100,
                              child: CommonDropdown(
                                height: 40,
                                initialText: state.activeTab?.method ?? 'GET',
                                //dropdownIcon: Icon(Icons.arrow_drop_down, color: Colors.white),
                                items: ["GET", "POST", "PUT", "DELETE"],
                                backgroundColor: AppThemeColor.commonBackground,
                                dropdownBackgroundColor: Colors.grey[800]!,
                                textStyle: TextStyle(color: Colors.white),
                                textPadding: EdgeInsets.only(right: 4, left: 15),
                                // Adjust text padding
                                iconPadding: EdgeInsets.only(left: 4),
                                // Adjust icon padding
                                padding: EdgeInsets.symmetric(horizontal: 4),
                                onChanged: (value) {
                                  // context.read<HomeBloc>().add(ChangeMethodEvent(uuid: widget.uuid,method: value));
                                  // state.activeTab. = value!;
                                  // print("Selected: $value");
                                  // print("ucd: ${widget.uuid}");
                                  context.read<HomeBloc>().add(
                                    ChangeMethodEvent(
                                      uuid: state.activeTab!.uuid,
                                      method: value,
                                    ),
                                  );

                                },
                              ),
                            );
  },
),
                            const VerticalDivider(
                              color: AppThemeColor.commonDropdownArrowColor,
                              thickness: 1,
                              indent: 5,
                              endIndent: 5,
                            ),
                            SizedBox(width: 8,),
                            BlocConsumer<HomeBloc, HomeState>(
                              listener: (context, state) {
                                //widget.urlController.text = state.activeTab!['url'] ?? '';
                              },
                              builder: (context, state) {
                             return Expanded(
                                child: TextField(
                                  style: TextStyle(color: Colors.white, fontSize: 12,),
                                  decoration: const InputDecoration(
                                      contentPadding: EdgeInsets.zero,
                                      hintText: 'https://example.com/services',
                                      hintStyle: TextStyle(
                                          color: Colors.grey, fontSize: 12,
                                          fontFamily: 'inter',
                                          fontWeight: FontWeight.w400),
                                      border: InputBorder.none,
                                      isDense: true
                                  ),
                                  controller: widget.urlController,
                                  onChanged: (value) {
                                    context.read<HomeBloc>().add(ChangeUrlEvent(url: value, uuid: widget.uuid));
                                    print("uur: ${widget.uuid}");
                                  },
                                )
                            );
  },
),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 8.0),

                    CommonButton(
                        buttonText: "Send",
                        height: 40,
                        width: 100,
                        backgroundColor: AppThemeColor.buttonBlue,
                        textStyle: mRegularTextStyle16(
                          textSize: 12,
                          textColor: AppThemeColor.white,),
                        callback: () {
                          if (selectedTabIndex == 0) {
                            sendRequestFromNone(context);
                          } else if (selectedTabIndex == 1) {
                            debugPrint("Send request with form data");
                            sendRequestFromFormData(context);
                          } else if (selectedTabIndex == 2) {
                            debugPrint("Send request with JSON");
                            sendRequestFromBodyJson(context);
                          } else{
                            sendRequestFromNone(context);
                          }
                        }
                    )
                  ],
                ),
              );
            },
          );
        },
      ),
    );}

  Map<String, String> combineHeaders(BuildContext context) {
    final headersBloc = context.read<HeadersBloc>();
    final authState = context.read<AuthSubViewsBloc>().state;

    final combinedHeaders = <String, String>{};

    for (var row in headersBloc.state.rows) {
      if (row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty) {
        combinedHeaders[row.key] = row.value;
      }
    }

    if (authState.authType == "Bearer Token" && authState.bearerTokenFields.token.isNotEmpty) {
      combinedHeaders['Authorization'] = 'Bearer ${authState.bearerTokenFields.token}';
    }

    if (authState.authType == "Basic Auth" &&
        authState.basicAuthFields.username.isNotEmpty &&
        authState.basicAuthFields.password.isNotEmpty) {
      final basicAuth = 'Basic ' +
          base64.encode(utf8.encode('${authState.basicAuthFields.username}:${authState.basicAuthFields.password}'));
      combinedHeaders['Authorization'] = basicAuth;
    }



    if (authState.authType == "API Key" &&
        authState.apiKeyFields.key.isNotEmpty &&
        authState.apiKeyFields.value.isNotEmpty && authState.apiKeyFields.addToHeaderOrQueryParams == 'Header') {
      combinedHeaders[authState.apiKeyFields.key] = authState.apiKeyFields.value;

      print('ss: ${authState.apiKeyFields.addToHeaderOrQueryParams}');
    }

    return combinedHeaders;
  }

  void sendRequestFromBodyJson(BuildContext context) {
    final state = context.read<JsonDataBloc>().state;
    final headers = combineHeaders(context);

    _SendRequestBody(body: state.jsonData, headers: headers);
  }

  void _SendRequestBody({required Map<String, dynamic>? body, required Map<String, String> headers}) {
    final url = widget.urlController.text.trim();
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please enter a valid URL.')));
      return;
    }

    context.read<RequestBloc>().add(SendRequestEvent(
      method: selectedMethod,
      url: url,
      headers: headers,
      body: body,
    ));
  }

  void sendRequestFromFormData(BuildContext context) {
    final formDataBloc = context.read<FormDataBloc>();
    final headers = combineHeaders(context);

    final combinedFormData = <String, String>{};
    for (var row in formDataBloc.state.rows) {
      if (row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty) {
        combinedFormData[row.key] = row.value;
      }
    }

    _SendRequestFormData(formData: combinedFormData, headers: headers);
  }

  void _SendRequestFormData({required Map<String, String> formData, required Map<String, String> headers}) {
    final url = widget.urlController.text.trim();
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please enter a valid URL.')));
      return;
    }

    context.read<RequestBloc>().add(SendRequestEventFormData(
      method: selectedMethod,
      url: url,
      headers: headers,
      formData: formData,
    ));
  }

  void sendRequestFromNone(BuildContext context) {
    final headers = combineHeaders(context);

    final url = widget.urlController.text.trim();
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please enter a valid URL.')));
      return;
    }

    context.read<RequestBloc>().add(SendRequestEvent(
      method: selectedMethod,
      url: url,
      headers: headers,
    ));
  }

}*/


class RequestSenderView extends StatefulWidget {
  final String uuid;

  const RequestSenderView({
    required this.uuid,
    Key? key,
  }) : super(key: key);

  @override
  State<RequestSenderView> createState() => _RequestSenderViewState();
}

class _RequestSenderViewState extends State<RequestSenderView> {
  late TextEditingController _urlController;
  late String _selectedMethod;

  @override
  void initState() {
    super.initState();
    _urlController = TextEditingController();
    _selectedMethod = "GET";
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateControllersFromActiveTab();
    // _urlController.text = "";
    // _selectedMethod = "GET";
  }

  void _updateControllersFromActiveTab() {
    final activeTab = context.read<HomeBloc>().state.activeTab;
    if (activeTab != null && activeTab.uuid == widget.uuid) {
      _urlController.text = activeTab.url;
      _selectedMethod = activeTab.method;
    } else{
      _urlController.text = "";
      _selectedMethod = "GET";
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<AuthSubViewsBloc, AuthSubViewsState>(
          // listenWhen: (previous, current) =>
          // previous.username != current.username ||
          //     previous.password != current.password,
          listener: (context, state) {
            //getting data from basic auth
            if (state.authType == "Basic Auth") {
              // Optionally show feedback or log the updated values
              debugPrint("UsernameRS: ${state.basicAuthFields.username}");
              debugPrint("PasswordRS: ${state.basicAuthFields.password}");
            }

            //getting data from bearer token
            if (state.authType == "Bearer Token") {
              // Optionally show feedback or log the updated values
              debugPrint("TokenRS: ${state.bearerTokenFields.token}");
            }

            debugPrint("AuthType: ${state.authType}");

            debugPrint("HeaderOrQueryParamsApiKey: ${state.apiKeyFields.addToHeaderOrQueryParams}");

            //getting data from api key
            if (state.authType == "API Key") {
              // Optionally show feedback or log the updated values
              debugPrint("ApiKey: ${state.apiKeyFields.key}");
              debugPrint("ApiValue: ${state.apiKeyFields.value}");
              debugPrint("ApiHeaderOrQueryParams: ${state.apiKeyFields.addToHeaderOrQueryParams}");
            }
            //getting data from jwt bearer
            debugPrint("algorithm: ${state.jwtBearerFields.algorithm}");
            debugPrint("secretToken: ${state.jwtBearerFields.secretToken}");
            debugPrint("payload: ${state.jwtBearerFields.payload}");
            debugPrint("requestHeaderprefix: ${state.jwtBearerFields.requestHeaderprefix}");
            debugPrint("jwtHeader: ${state.jwtBearerFields.jwtHeader}");

            debugPrint("jwtTokenToHeaderOrQueryParams: ${state.jwtBearerFields.jwtTokenToHeaderOrQueryParams}");

            debugPrint('AuthSubViewsBloc Listener');
          },
        ),
        BlocListener<QueryParamsBloc, QueryParamsState>(
          listener: (context, state) {
            final baseUrl = _urlController.text.split('?').first;
            final queryString = state.generateQueryString();
            final newUrl = queryString.isNotEmpty ? '$baseUrl$queryString' : baseUrl;

            if (_urlController.text != newUrl) {
              _urlController.text = newUrl;
            }
            debugPrint('QueryParams Listener:');
          },
        ),
      ],
      child: BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        // final activeTab = state.activeTab;
        // if (activeTab == null || activeTab.uuid != widget.uuid) {
        //   return Container(); // Return an empty container if this tab is not active
        // }
        _urlController.text = state.activeTab?.url??"";

        return Container(
          padding: EdgeInsets.only(left: 20, right: 20),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Container(
                  height: 40,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: AppThemeColor.commonBorderColor,
                      width: 1.0,
                    ),
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(width: 8.0),
                      SizedBox(
                        width: 100,
                        child: CommonDropdown(
                          key: ValueKey(widget.uuid),
                          initialText: state.activeTab?.method,
                          items: const ["GET", "POST", "PUT", "DELETE", "PATCH"],
                          backgroundColor: AppThemeColor.commonBackground,
                          dropdownBackgroundColor: Colors.grey[800]!,
                          textStyle: TextStyle(color: Colors.white),
                          textPadding: EdgeInsets.only(right: 4, left: 15),
                          // Adjust text padding
                          iconPadding: EdgeInsets.only(left: 4),
                          // Adjust icon padding
                          padding: EdgeInsets.symmetric(horizontal: 4),
                          onChanged: (value) {
                            context.read<HomeBloc>().add(
                              ChangeMethodEvent(
                                uuid: widget.uuid,
                                method: value,
                              ),
                            );
                            setState(() {
                              _selectedMethod = value;
                            });
                          },
                        ),
                      ),
                      const VerticalDivider(
                        color: AppThemeColor.commonDropdownArrowColor,
                        thickness: 1,
                        indent: 5,
                        endIndent: 5,
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: TextField(
                          controller: _urlController..selection = TextSelection.fromPosition(TextPosition(offset: (state.activeTab?.url??'').length)),
                          style: const TextStyle(color: Colors.white, fontSize: 12,),
                          decoration: const InputDecoration(
                              contentPadding: EdgeInsets.zero,
                              hintText: 'https://example.com/services',
                              hintStyle: TextStyle(
                                  color: Colors.grey, fontSize: 12,
                                  fontFamily: 'inter',
                                  fontWeight: FontWeight.w400),
                              border: InputBorder.none,
                              isDense: true,
                          ),
                          onChanged: (value) {
                            context.read<HomeBloc>().add(
                              ChangeUrlEvent(
                                uuid: widget.uuid,
                                url: value,
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 8.0),
              BlocBuilder<TabBloc, TabState>(

                  builder: (context, state) {
                    final selectedTabIndex = state is TabUpdated ? state.tabIndex : 0;
                  return CommonButton(
                  buttonText: "Send",
                  height: 40,
                  width: 100,
                  backgroundColor: AppThemeColor.buttonBlue,
                  textStyle: mRegularTextStyle16(
                    textSize: 12,
                    textColor: AppThemeColor.white,
                  ),
                  callback: () {
                    if (selectedTabIndex == 0) {
                      sendRequestFromNone(context);
                    } else if (selectedTabIndex == 1) {
                      debugPrint("Send request with form data");
                      sendRequestFromFormData(context);
                    } else if (selectedTabIndex == 2) {
                      debugPrint("Send request with JSON");
                      sendRequestFromBodyJson(context);
                    } else{
                      sendRequestFromNone(context);
                    }
                  });
  },
),
            ],
          ),
        );
      },
    ),
);
  }

  Map<String, String> combineHeaders(BuildContext context) {
    final headersBloc = context.read<HeadersBloc>();
    final authState = context.read<AuthSubViewsBloc>().state;

    final combinedHeaders = <String, String>{};

    for (var row in headersBloc.state.rows) {
      if (row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty) {
        combinedHeaders[row.key] = row.value;
      }
    }

    if (authState.authType == "Bearer Token" && authState.bearerTokenFields.token.isNotEmpty) {
      combinedHeaders['Authorization'] = 'Bearer ${authState.bearerTokenFields.token}';
    }

    if (authState.authType == "Basic Auth" &&
        authState.basicAuthFields.username.isNotEmpty &&
        authState.basicAuthFields.password.isNotEmpty) {
      final basicAuth = 'Basic ' +
          base64.encode(utf8.encode('${authState.basicAuthFields.username}:${authState.basicAuthFields.password}'));
      combinedHeaders['Authorization'] = basicAuth;
    }



    if (authState.authType == "API Key" &&
        authState.apiKeyFields.key.isNotEmpty &&
        authState.apiKeyFields.value.isNotEmpty && authState.apiKeyFields.addToHeaderOrQueryParams == 'Header') {
      combinedHeaders[authState.apiKeyFields.key] = authState.apiKeyFields.value;

      print('ss: ${authState.apiKeyFields.addToHeaderOrQueryParams}');
    }

    print('headersData::: ${combinedHeaders}');

    return combinedHeaders;
  }

  void sendRequestFromBodyJson(BuildContext context) {
    final state = context.read<JsonDataBloc>().state;
    final headers = combineHeaders(context);

    _SendRequestBody(body: state.jsonData, headers: headers);

    final homeBloc = context.read<HomeBloc>();
    final activeTab = homeBloc.state.activeTab;
    if (activeTab != null) {
      homeBloc.add(SaveRequestToHistoryEvent(
        requestName: activeTab.tabName,
        method: activeTab.method,
        url: _urlController.text.trim(),
        jsonData: state.jsonData,
      ));
    }

  }

  void _SendRequestBody({required Map<String, dynamic>? body, required Map<String, String> headers}) {
    final url = _urlController.text.trim();
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please enter a valid URL.')));
      return;
    }

    context.read<RequestBloc>().add(SendRequestEvent(
      method: _selectedMethod,
      url: url,
      headers: headers,
      body: body,
      tabUuid: widget.uuid, // Add the tab UUID to associate the response with this tab
    ));
  }

  void sendRequestFromFormData(BuildContext context) {
    final formDataBloc = context.read<FormDataBloc>();
    final headers = combineHeaders(context);
    final state = context.read<JsonDataBloc>().state;

    final combinedFormData = <String, String>{};
    for (var row in formDataBloc.state.rows) {
      if (row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty) {
        combinedFormData[row.key] = row.value;
      }
    }
    _SendRequestFormData(formData: combinedFormData, headers: headers);

    final homeBloc = context.read<HomeBloc>();
    final activeTab = homeBloc.state.activeTab;
    if (activeTab != null) {
      homeBloc.add(SaveRequestToHistoryEvent(
        requestName: activeTab.tabName,
        method: activeTab.method,
        url: _urlController.text.trim(),
        jsonData: state.jsonData,
      ));
    }

  }

  void _SendRequestFormData({required Map<String, String> formData, required Map<String, String> headers}) {
    final url = _urlController.text.trim();
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please enter a valid URL.')));
      return;
    }

    context.read<RequestBloc>().add(SendRequestEventFormData(
      method: _selectedMethod,
      url: url,
      headers: headers,
      formData: formData,
      tabUuid: widget.uuid, // Add the tab UUID to associate the response with this tab
    ));
  }

  void sendRequestFromNone(BuildContext context) {
    final headers = combineHeaders(context);
    final state = context.read<JsonDataBloc>().state;
    final url = _urlController.text.trim();
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please enter a valid URL.')));
      return;
    }

    context.read<RequestBloc>().add(SendRequestEvent(
      method: _selectedMethod,
      url: url,
      headers: headers,
      tabUuid: widget.uuid, // Add the tab UUID to associate the response with this tab
    ));

    final homeBloc = context.read<HomeBloc>();
    final activeTab = homeBloc.state.activeTab;
    if (activeTab != null) {
      homeBloc.add(SaveRequestToHistoryEvent(
        requestName: activeTab.tabName,
        method: activeTab.method,
        url: _urlController.text.trim(),
        jsonData: state.jsonData,
      ));
    }

  }

}



/*class RequestSenderView extends StatefulWidget {
  final String uuid;

  const RequestSenderView({
    required this.uuid,
    Key? key,
  }) : super(key: key);

  @override
  State<RequestSenderView> createState() => _RequestSenderViewState();
}

class _RequestSenderViewState extends State<RequestSenderView> {
  late TextEditingController _urlController;
  late String _selectedMethod;

  @override
  void initState() {
    super.initState();
    _urlController = TextEditingController();
    _selectedMethod = "GET";
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateControllersFromActiveTab();
  }

  void _updateControllersFromActiveTab() {
    final activeTab = context.read<HomeBloc>().state.activeTab;
    if (activeTab != null && activeTab.uuid == widget.uuid) {
      _urlController.text = activeTab.url;
      _selectedMethod = activeTab.method;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        final activeTab = state.activeTab;
        if (activeTab == null || activeTab.uuid != widget.uuid) {
          return Container(); // Return an empty container if this tab is not active
        }

        return Container(
          padding: EdgeInsets.only(left: 20, right: 20),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Container(
                  height: 40,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: AppThemeColor.commonBorderColor,
                      width: 1.0,
                    ),
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(width: 8.0),
                      SizedBox(
                        width: 100,
                        child: CommonDropdown(
                          initialText: _selectedMethod,
                          items: ["GET", "POST", "PUT", "DELETE"],
                          onChanged: (value) {
                            context.read<HomeBloc>().add(
                              ChangeMethodEvent(
                                uuid: widget.uuid,
                                method: value!,
                              ),
                            );
                            setState(() {
                              _selectedMethod = value;
                            });
                          },
                        ),
                      ),
                      const VerticalDivider(
                        color: AppThemeColor.commonDropdownArrowColor,
                        thickness: 1,
                        indent: 5,
                        endIndent: 5,
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: TextField(
                          controller: _urlController,
                          onChanged: (value) {
                            context.read<HomeBloc>().add(
                              ChangeUrlEvent(
                                uuid: widget.uuid,
                                url: value,
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 8.0),
              CommonButton(
                  buttonText: "Send",
                  height: 40,
                  width: 100,
                  backgroundColor: AppThemeColor.buttonBlue,
                  textStyle: mRegularTextStyle16(
                    textSize: 12,
                    textColor: AppThemeColor.white,
                  ),
                  callback: () {

                  }),
            ],
          ),
        );
      },
    );
  }

  Map<String, String> combineHeaders(BuildContext context) {
    final headersBloc = context.read<HeadersBloc>();
    final authState = context.read<AuthSubViewsBloc>().state;

    final combinedHeaders = <String, String>{};

    for (var row in headersBloc.state.rows) {
      if (row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty) {
        combinedHeaders[row.key] = row.value;
      }
    }

    if (authState.authType == "Bearer Token" && authState.bearerTokenFields.token.isNotEmpty) {
      combinedHeaders['Authorization'] = 'Bearer ${authState.bearerTokenFields.token}';
    }

    if (authState.authType == "Basic Auth" &&
        authState.basicAuthFields.username.isNotEmpty &&
        authState.basicAuthFields.password.isNotEmpty) {
      final basicAuth = 'Basic ' +
          base64.encode(utf8.encode('${authState.basicAuthFields.username}:${authState.basicAuthFields.password}'));
      combinedHeaders['Authorization'] = basicAuth;
    }



    if (authState.authType == "API Key" &&
        authState.apiKeyFields.key.isNotEmpty &&
        authState.apiKeyFields.value.isNotEmpty && authState.apiKeyFields.addToHeaderOrQueryParams == 'Header') {
      combinedHeaders[authState.apiKeyFields.key] = authState.apiKeyFields.value;

      print('ss: ${authState.apiKeyFields.addToHeaderOrQueryParams}');
    }

    return combinedHeaders;
  }

  void sendRequestFromBodyJson(BuildContext context) {
    final state = context.read<JsonDataBloc>().state;
    final headers = combineHeaders(context);

    _SendRequestBody(body: state.jsonData, headers: headers);
  }

  void _SendRequestBody({required Map<String, dynamic>? body, required Map<String, String> headers}) {
    final url = widget.urlController.text.trim();
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please enter a valid URL.')));
      return;
    }

    context.read<RequestBloc>().add(SendRequestEvent(
      method: selectedMethod,
      url: url,
      headers: headers,
      body: body,
    ));
  }

  void sendRequestFromFormData(BuildContext context) {
    final formDataBloc = context.read<FormDataBloc>();
    final headers = combineHeaders(context);

    final combinedFormData = <String, String>{};
    for (var row in formDataBloc.state.rows) {
      if (row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty) {
        combinedFormData[row.key] = row.value;
      }
    }

    _SendRequestFormData(formData: combinedFormData, headers: headers);
  }

  void _SendRequestFormData({required Map<String, String> formData, required Map<String, String> headers}) {
    final url = widget.urlController.text.trim();
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please enter a valid URL.')));
      return;
    }

    context.read<RequestBloc>().add(SendRequestEventFormData(
      method: selectedMethod,
      url: url,
      headers: headers,
      formData: formData,
    ));
  }

  void sendRequestFromNone(BuildContext context) {
    final headers = combineHeaders(context);

    final url = widget.urlController.text.trim();
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please enter a valid URL.')));
      return;
    }

    context.read<RequestBloc>().add(SendRequestEvent(
      method: selectedMethod,
      url: url,
      headers: headers,
    ));
  }

}*/



//tested code
/*class RequestSenderView extends StatefulWidget {
  const RequestSenderView({super.key});

  @override
  State<RequestSenderView> createState() => _RequestSenderViewState();
}

class _RequestSenderViewState extends State<RequestSenderView> {
  // Controllers for the URL field and default method
  final TextEditingController urlController = TextEditingController();
  String selectedMethod = "GET";

  @override
  void initState() {

    super.initState();

       // Listen for changes to the URL controller
       urlController.addListener(() {

      final currentText = urlController.text.trim();

      // Dispatch ClearQueryParamsEvent when the URL field is cleared
      if (currentText.isEmpty) {
        context.read<QueryParamsBloc>().add(ClearQueryParamsEvent());
      }

    });


  }


  @override
  Widget build(BuildContext context) {



    return MultiBlocListener(
     listeners: [

       BlocListener<AuthSubViewsBloc, AuthSubViewsState>(
         // listenWhen: (previous, current) =>
         // previous.username != current.username ||
         //     previous.password != current.password,
         listener: (context, state) {
           //getting data from basic auth
           if (state.authType == "Basic Auth") {
             // Optionally show feedback or log the updated values
             debugPrint("UsernameRS: ${state.basicAuthFields.username}");
             debugPrint("PasswordRS: ${state.basicAuthFields.password}");
           }

           //getting data from bearer token
           if (state.authType == "Bearer Token") {
             // Optionally show feedback or log the updated values
             debugPrint("TokenRS: ${state.bearerTokenFields.token}");
           }

           debugPrint("AuthType: ${state.authType}");

           debugPrint("AddToHeaderOrQueryParams: ${state.apiKeyFields.addToHeaderOrQueryParams}");

            //getting data from api key
           if (state.authType == "API Key") {
             // Optionally show feedback or log the updated values
             debugPrint("ApiKey: ${state.apiKeyFields.key}");
             debugPrint("ApiValue: ${state.apiKeyFields.value}");
             debugPrint("ApiHeaderOrQueryParams: ${state.apiKeyFields.addToHeaderOrQueryParams}");
           }
             //getting data from jwt bearer
             debugPrint("algorithm: ${state.jwtBearerFields.algorithm}");
             debugPrint("secretToken: ${state.jwtBearerFields.secretToken}");
             debugPrint("payload: ${state.jwtBearerFields.payload}");
             debugPrint("requestHeaderprefix: ${state.jwtBearerFields.requestHeaderprefix}");
             debugPrint("jwtHeader: ${state.jwtBearerFields.jwtHeader}");

             debugPrint("jwtTokenToHeaderOrQueryParams: ${state.jwtBearerFields.jwtTokenToHeaderOrQueryParams}");

             debugPrint('AuthSubViewsBloc Listener');
         },
       ),

       BlocListener<QueryParamsBloc, QueryParamsState>(
      listener: (context, state) {
        final baseUrl = urlController.text.split('?').first;
        final queryString = state.generateQueryString();
        final newUrl = queryString.isNotEmpty ? '$baseUrl$queryString' : baseUrl;

        if (urlController.text != newUrl) {
          urlController.text = newUrl;
        }
        debugPrint('QueryParams Listener:');
      },
     ),


  ],
  child: BlocBuilder<TabBloc, TabState>(
  builder: (context, state) {
    final selectedTabIndex = state is TabUpdated ? state.tabIndex : 0;
    return  BlocBuilder<AuthSubViewsBloc, AuthSubViewsState>(
  builder: (context, state) {
    print( "Selected Auth Type: ${state.authType}");
    return Container(
      padding: EdgeInsets.only(left: 20, right: 20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [

          Expanded(
            child: Container(
              height: 40,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                border: Border.all(
                  color: AppThemeColor.commonBorderColor,
                  width: 1.0,
                ),
                borderRadius: BorderRadius.circular(4.0),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(width: 8.0),
                  SizedBox(
                    width: 100,
                    child: CommonDropdown(
                      height: 40,
                      initialText: "GET",
                      //dropdownIcon: Icon(Icons.arrow_drop_down, color: Colors.white),
                      items: ["GET", "POST", "PUT", "DELETE"],
                      backgroundColor: AppThemeColor.commonBackground,
                      dropdownBackgroundColor: Colors.grey[800]!,
                      textStyle: TextStyle(color: Colors.white),
                      textPadding: EdgeInsets.only(right: 4, left: 15),
                      // Adjust text padding
                      iconPadding: EdgeInsets.only(left: 4),
                      // Adjust icon padding
                      padding: EdgeInsets.symmetric(horizontal: 4),
                      onChanged: (value) {
                        setState(() {
                          selectedMethod = value!;
                        });
                        print("Selected: $value");
                      },
                    ),
                  ),
                  const VerticalDivider(
                    color: AppThemeColor.commonDropdownArrowColor,
                    thickness: 1,
                    indent: 5,
                    endIndent: 5,
                  ),
                  SizedBox(width: 8,),
                  Expanded(
                      child: TextField(
                        style: TextStyle(color: Colors.white, fontSize: 12,),
                        decoration: const InputDecoration(
                            contentPadding: EdgeInsets.zero,
                            hintText: 'https://example.com/services',
                            hintStyle: TextStyle(
                                color: Colors.grey, fontSize: 12,
                                fontFamily: 'inter',
                                fontWeight: FontWeight.w400),
                            border: InputBorder.none,
                            isDense: true
                        ),
                        controller: urlController,
                      )
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(width: 8.0),

          CommonButton(
                  buttonText: "Send",
                  height: 40,
                  width: 100,
                  backgroundColor: AppThemeColor.buttonBlue,
                  textStyle: mRegularTextStyle16(
                    textSize: 12,
                    textColor: AppThemeColor.white,),
                   callback: () {
                    if (selectedTabIndex == 0) {
                      sendRequestFromNone(context);
                    } else if (selectedTabIndex == 1) {
                      debugPrint("Send request with form data");
                      sendRequestFromFormData(context);
                    } else if (selectedTabIndex == 2) {
                      debugPrint("Send request with JSON");
                      sendRequestFromBodyJson(context);
                    } else{
                      sendRequestFromNone(context);
                    }
                    //sendRequestFromQueryParams(context);

                    // final selectedAuthType = context.read<AuthorizationBloc>().state.selectedAuthType;
                    // print("Selected Auth Type: $selectedAuthType");

                    // if (selectedAuthType == "Basic Auth" && authData != null) {
                    //   final username = authData.username;
                    //   final password = authData.password;
                    //   print("Sending request with Basic Auth: $username:$password");
                    // }
                   }
          )

        ],
      ),
    );
  },
);
  },
),
);
  }


    void sendRequestFromBodyJson(BuildContext context) {

    var state = context.read<JsonDataBloc>().state;

    final headersBloc = BlocProvider.of<HeadersBloc>(context);

    final headerData = headersBloc.state.rows
        .where((row) => row.isSelected)
        .map((row) => {'key': row.key, 'value': row.value, 'description': row.description})
        .toList();

    debugPrint("headerData: ${headerData}");

    final headerRows = headersBloc.state.rows;

    _SendRequestBody(body: state.jsonData, header: headerData, headerRows: headerRows);

  }

  void _SendRequestBody({
    required Map<String, dynamic>? body,
    required List<HeaderRow> headerRows, required List<Map<String, String>> header,
  }) {

    debugPrint("Send button tapped!");
    debugPrint("body:::: $body");

    try {
      final url = urlController.text.trim();
      if (url.isEmpty) {
        debugPrint("URL is empty");
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please enter a valid URL.')),
        );
        return;
      }
      debugPrint("URL: $url");

      final combinedHeaders = <String, String>{};
      for (var row in headerRows) {
        if (row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty) {
          combinedHeaders[row.key] = row.value;
        }
      }
      debugPrint("Combined Headers: $combinedHeaders");

      final requestBloc = BlocProvider.of<RequestBloc>(context);
      requestBloc.add(SendRequestEvent(
        method: selectedMethod,
        url: url,
        headers: combinedHeaders,
        body: body,
      ));
      debugPrint("Request sent with method: $selectedMethod");
    } catch (e) {
      debugPrint("Error in callback: $e");
    }
  }



  void sendRequestFromFormData(BuildContext context) {

    final formDataBloc = BlocProvider.of<FormDataBloc>(context);
    final formDatas = formDataBloc.state.rows
        .where((row) => row.isSelected)
        .map((row) => {'key': row.key, 'value': row.value, 'description': row.description})
        .toList();
        //debugPrint("formData: ${formDatas}");


    final formDataRows = formDataBloc.state.rows;


    final headersBloc = BlocProvider.of<HeadersBloc>(context);

    final headerData = headersBloc.state.rows
        .where((row) => row.isSelected)
        .map((row) => {'key': row.key, 'value': row.value, 'description': row.description})
        .toList();

    //debugPrint("headerData: ${headerData}");

    final headerRows = headersBloc.state.rows;


    _SendRequestFormData(formData: formDatas, formDataRows: formDataRows, header: headerData,  headerRows: headerRows);
  }

  void _SendRequestFormData({
    required List<FormDataRow> formDataRows, required List<Map<String, String>> formData,
    required List<HeaderRow> headerRows, required List<Map<String, String>> header,
  }) {
    debugPrint("Send button tapped!");
    //debugPrint("body:::: $formData");

    try {
      final url = urlController.text.trim();
      if (url.isEmpty) {
        debugPrint("URL is empty");
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please enter a valid URL.')),
        );
        return;
      }
      debugPrint("URL: $url");

      final combinedHeaders = <String, String>{};

      for (var row in headerRows) {
        if (row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty) {
          combinedHeaders[row.key] = row.value;
        }
      }

      final authState = context.read<AuthSubViewsBloc>().state;

      if (authState.authType == "Bearer Token") {
        final token = authState.bearerTokenFields.token;
        if (token.isNotEmpty) {
          combinedHeaders['Authorization'] = 'Bearer $token';
        }
      }

      if (authState.authType == "Basic Auth") {
        final username = authState.basicAuthFields.username;
        final password = authState.basicAuthFields.password;
        String basicAuth =
            'Basic ' + base64.encode(utf8.encode('$username:$password'));

        debugPrint(basicAuth);

        if (username.isNotEmpty && password.isNotEmpty) {
          combinedHeaders['Authorization'] = basicAuth;
        }
      }


      debugPrint("Combined Headers: $combinedHeaders");


      final combinedFormdata = <String, String>{};
      for (var row in formDataRows) {
        if (row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty) {
          combinedFormdata[row.key] = row.value;
        }
      }
      debugPrint("Combined Formdata: $combinedFormdata");


      final requestBloc = BlocProvider.of<RequestBloc>(context);

      requestBloc.add(SendRequestEventFormData(
        method: selectedMethod,
        url: url,
        headers: combinedHeaders,
        formData: combinedFormdata,
      ));
      debugPrint("Method: $selectedMethod");
    } catch (e) {
      debugPrint("Error in callback: $e");
    }
  }





  void sendRequestFromNone(BuildContext context) {


    final headersBloc = BlocProvider.of<HeadersBloc>(context);

    final headerData = headersBloc.state.rows
        .where((row) => row.isSelected)
        .map((row) => {'key': row.key, 'value': row.value, 'description': row.description})
        .toList();

    debugPrint("headerData: ${headerData}");

    final headerRows = headersBloc.state.rows;

    _SendRequestNone(header: headerData, headerRows: headerRows);


  }

  void _SendRequestNone({
    required List<HeaderRow> headerRows, required List<Map<String, String>> header,
  }) {
    debugPrint("Send button tapped!");

    try {
      final url = urlController.text.trim();
      if (url.isEmpty) {
        debugPrint("URL is empty");
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please enter a valid URL.')),
        );
        return;
      }
      debugPrint("URL: $url");

      final combinedHeaders = <String, String>{};
      for (var row in headerRows) {
        if (row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty) {
          combinedHeaders[row.key] = row.value;
        }
      }

      debugPrint("Combined Headers: $combinedHeaders");

      final requestBloc = BlocProvider.of<RequestBloc>(context);
      requestBloc.add(SendRequestEvent(
        method: selectedMethod,
        url: url,
        headers: combinedHeaders,
      ));
      debugPrint("Request sent with method: $selectedMethod");
    } catch (e) {
      debugPrint("Error in callback: $e");
    }
  }

}*/


/* void sendRequestFromQueryParams(BuildContext context) {

    final headersBloc = BlocProvider.of<HeadersBloc>(context);
    final headerData = headersBloc.state.rows
        .where((row) => row.isSelected)
        .map((row) => {'key': row.key, 'value': row.value, 'description': row.description})
        .toList();

    debugPrint("headerData: ${headerData}");

    final headerRows = headersBloc.state.rows;


    //var bodyJson = context.read<JsonDataBloc>().state;


    _SendRequestQueryParams(header: headerData,  headerRows: headerRows);
  }

  void _SendRequestQueryParams({
    //required List<FormDataRow> formDataRows, required List<Map<String, String>> formData,
    required List<HeaderRow> headerRows, required List<Map<String, String>> header,
    //required Map<String, dynamic>? body,
  }) {
    debugPrint("Send button tapped!");
    //debugPrint("body:::: $formData");

    try {
      final url = urlController.text.trim();
      if (url.isEmpty) {
        debugPrint("URL is empty");
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please enter a valid URL.')),
        );
        return;
      }
      debugPrint("URL: $url");

      final combinedHeaders = <String, String>{};

      for (var row in headerRows) {
        if (row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty) {
          combinedHeaders[row.key] = row.value;
        }
      }

      debugPrint("Combined Headers: $combinedHeaders");




      final requestBloc = BlocProvider.of<RequestBloc>(context);
      requestBloc.add(SendRequestEventFormData(
        method: selectedMethod,
        url: url,
        headers: combinedHeaders,
      ));
      debugPrint("Request sent with method: $selectedMethod");
    } catch (e) {
      debugPrint("Error in callback: $e");
    }
  }*/













//working with static data
/*void _SendRequest({required Map<String, dynamic>? body, required List<Map<String, String>> header}) {
    print("Send button tapped!");
    print("body:::: $body");
    try {
      // Define static headers and body
      final staticHeaders = <String, String>{
        "Content-Type": "application/json",
        "Postman-Token": "78b7080c-6531-4341-a347-074eff13b340",
        "Host": "sankar.posservice.dinetestapi.com",
      };

      final staticBody = {
        "inputData": {
          "productId": 6232140,
        },
      };

      //final bodyJson = jsonDecode(jsonController as String) as Map<String, dynamic>;

      // Print static headers and body
      print("Static Headers: $staticHeaders");

      // Validate the URL
      final url = urlController.text.trim();
      if (url.isEmpty) {
        print("URL is empty");
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please enter a valid URL.')),
        );
        return;
      }
      print("URL: $url");

      // Dispatch the SendRequestEvent
      final requestBloc = BlocProvider.of<RequestBloc>(context);
      requestBloc.add(SendRequestEvent(
        method: selectedMethod,
        url: url,
        headers: staticHeaders,
        body: body,
      ));
      print("Request sent with method: $selectedMethod");
    } catch (e) {
      print("Error in callback: $e");
    }
  }*/