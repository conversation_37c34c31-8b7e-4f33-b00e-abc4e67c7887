import 'dart:js_interop';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:postman_flutter/features/home/<USER>/send_request/request_event.dart';
import 'package:postman_flutter/features/home/<USER>/send_request/request_state.dart';
import 'dart:convert';


class RequestBloc extends Bloc<RequestEvent, RequestState> {
  RequestBloc() : super(RequestInitialState()) {
    on<SendRequestEvent>(_onSendRequest);
    on<SendRequestEventFormData>(_onSendRequestFormData);
    on<SendRequestEventQueryParams>(_onSendRequestQueryParams);
  }

  Future<void> _onSendRequest(
      SendRequestEvent event,
      Emitter<RequestState> emit,
      ) async {
    emit(RequestLoadingState());
    try {
      final response = await _sendRequest(
        method: event.method,
        url: event.url,
        headers: event.headers,
        body: event.body,
      );
      emit(RequestSuccessState(response, event.tabUuid));
    } catch (e) {
      emit(RequestErrorState(e.toString()));
    }
  }



  Future<dynamic> _sendRequest({
    required String method,
    required String url,
    Map<String, String>? headers,
    Map<String, dynamic>? body,
  }) async {
    http.Response response;
    final uri = Uri.parse(url);

    switch (method.toUpperCase()) {
      case 'POST':
        response = await http.post(uri, headers: headers, body: json.encode(body));
        break;
      case 'PUT':
        response = await http.put(uri, headers: headers, body: json.encode(body));
        break;
      case 'DELETE':
        response = await http.delete(uri, headers: headers);
        break;
      case 'GET':
      default:
        response = await http.get(uri, headers: headers);
        break;
    }

    if (response.statusCode == 200) {
      debugPrint('rsp: ${response.body}');
      final decodedBody = json.decode(response.body);

      // Ensure the correct type is returned
      if (decodedBody is List<dynamic>) {
        debugPrint('Response is a List');
        return decodedBody; // Return the JSON array
      } else if (decodedBody is Map<String, dynamic>) {
        debugPrint('Response is a Map');
        return decodedBody; // Return the JSON object
      } else {
        throw Exception('Unexpected response format: ${decodedBody.runtimeType}');
      }
    } else {
      throw Exception('HTTP Error: ${response.statusCode} - ${response.reasonPhrase}');
    }
  }



  Future<void> _onSendRequestFormData(
      SendRequestEventFormData event,
      Emitter<RequestState> emit,
      ) async {
    emit(RequestLoadingState());

    try {
      final response = await _sendRequestFormData(
        method: event.method,
        url: event.url,
        headers: event.headers,
        formData: event.formData,
      );

      debugPrint('response: ${response}');

      emit(RequestSuccessState(response, event.tabUuid));
    } catch (e) {
      emit(RequestErrorState(e.toString()));
    }
  }

  Future<dynamic> _sendRequestFormData({
    required String method,
    required String url,
    Map<String, String>? headers,
    Map<String, String>? formData,
  }) async {
    final uri = Uri.parse(url);

    debugPrint('formD:: ${formData}');

    try {
      late http.Response response;

      switch (method.toUpperCase()) {

        case 'POST':

        case 'POST':
          response = await http.post(uri,
              headers: headers, body: formData);
          break;

        case 'PUT':
          final request = http.MultipartRequest(method, uri)
            ..headers.addAll(headers ?? {})
            ..fields.addAll(formData ?? {});
          final streamedResponse = await request.send();
          final responseString = await streamedResponse.stream.bytesToString();
          response = http.Response(responseString, streamedResponse.statusCode);
          break;

        case 'DELETE':
          response = await http.delete(uri, headers: headers);
          break;

        case 'GET':
        default:
          response = await http.get(uri, headers: headers);
          break;
      }

      if (response.statusCode == 200) {
        // Parse response dynamically
        final decodedResponse = json.decode(response.body);
        return decodedResponse;
      } else {
        throw Exception(
            'Error: ${response.reasonPhrase}, Status Code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Request failed: $e');
    }
  }



  Future<void> _onSendRequestQueryParams(
      SendRequestEventQueryParams event,
      Emitter<RequestState> emit,
      ) async {
    emit(RequestLoadingState());

    try {
      final response = await _sendRequestQueryParams(
        method: event.method,
        url: event.url,
        headers: event.headers,
        body: event.body,
        formData: event.formData,
      );

      debugPrint('response: ${response}');
      emit(RequestSuccessState(response, event.tabUuid));
    } catch (e) {
      emit(RequestErrorState(e.toString()));
    }
  }

  Future<Map<String, String>> _sendRequestQueryParams({
    required String method,
    required String url,
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    Map<String, String>? formData,
  }) async {
    http.Response response;
    final uri = Uri.parse(url);

    switch (method.toUpperCase()) {
      case 'POST':
        response = await http.post(uri,
            headers: headers, body: formData);
        break;
      case 'PUT':
        response = await http.put(uri,
            headers: headers, body: formData);
        break;
      case 'DELETE':
        response = await http.delete(uri, headers: headers);
        break;
      case 'GET':
      default:
        response = await http.get(uri, headers: headers);
        break;
    }

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Error: ${response.reasonPhrase}');
    }
  }



}



/*class RequestBloc extends Bloc<RequestEvent, RequestState> {
  RequestBloc() : super(RequestInitialState()) {
    on<SendRequestEvent>(_onSendRequest);
  }

  Future<void> _onSendRequest(
      SendRequestEvent event,
      Emitter<RequestState> emit,
      ) async {
    emit(RequestLoadingState());
    try {
      final response = await _sendRequest(
        method: event.method,
        url: event.url,
        headers: event.headers,
        body: event.body,
      );
      emit(RequestSuccessState(response));
    } catch (e) {
      emit(RequestErrorState(e.toString()));
    }
  }

  Future<Map<String, dynamic>> _sendRequest({
    required String method,
    required String url,
    Map<String, String>? headers,
    Map<String, dynamic>? body,
  }) async {
    http.Response response;
    final uri = Uri.parse(url);

    switch (method.toUpperCase()) {
      case 'POST':
        response = await http.post(uri,
            headers: headers, body: json.encode(body));
        break;
      case 'PUT':
        response = await http.put(uri,
            headers: headers, body: json.encode(body));
        break;
      case 'DELETE':
        response = await http.delete(uri, headers: headers);
        break;
      case 'GET':
      default:
        response = await http.get(uri, headers: headers);
        break;
    }

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Error: ${response.reasonPhrase}');
    }
  }
}*/



//working code
/*  Future<Map<String, dynamic>> _sendRequest({
    required String method,
    required String url,
    Map<String, String>? headers,
    Map<String, dynamic>? body,
  }) async {
    http.Response response;
    final uri = Uri.parse(url);

    switch (method.toUpperCase()) {
      case 'POST':
        response = await http.post(uri,
            headers: headers, body: json.encode(body));
        break;
      case 'PUT':
        response = await http.put(uri,
            headers: headers, body: json.encode(body));
        break;
      case 'DELETE':
        response = await http.delete(uri, headers: headers);
        break;
      case 'GET':
      default:
        response = await http.get(uri, headers: headers);
        break;
    }

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Error: ${response.reasonPhrase}');
    }
  }*/



//old
/*  Future<Map<String, String>> _sendRequestFormData({
    required String method,
    required String url,
    Map<String, String>? headers,
    Map<String, String>? formData,
  }) async {
    http.Response response;
    final uri = Uri.parse(url);
    final http.Client httpClient;

    switch (method.toUpperCase()) {

      case 'POST':
        response = await http.post(uri,
            headers: headers, body: formData);
        break;

      case 'PUT':
        response = await http.put(uri,
            headers: headers, body: formData);
        break;
      case 'DELETE':
        response = await http.delete(uri, headers: headers);
        break;
      case 'GET':
      default:
        response = await http.get(uri, headers: headers);
        break;
    }

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Error: ${response.reasonPhrase}');
    }
  }*/


/*final request = http.MultipartRequest(method, uri);
            request.headers.addAll(headers ?? {});
            //request.fields.addAll( formData ?? {});
            request.fields.addAll({
            'agentId': '00',
            'monthKey': 'hbhhb'
             });
            // ..headers.addAll(headers ?? {})
            // ..fields.addAll(formData ?? {});
          final streamedResponse = await request.send();
          final responseString = await streamedResponse.stream.bytesToString();
          response = http.Response(responseString, streamedResponse.statusCode);
          break;*/