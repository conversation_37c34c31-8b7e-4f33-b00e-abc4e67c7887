
import 'package:hive/hive.dart';

import '../tabs_views/header/bloc/headers_model.dart';

class ApiCollection {
  String? collectionName;
  List<ApiFolder>? children;
  String id; // Add the ID field

  ApiCollection({this.collectionName, this.children, required this.id});

  factory ApiCollection.fromJson(Map<String, dynamic> json) {
    return ApiCollection(
      collectionName: json['collectionName'] as String?,
      children: (json['children'] as List<dynamic>?)
          ?.map((child) => ApiFolder.fromJson(child))
          .toList(),
      id: json['id'] as String, // Deserialize the ID
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'collectionName': collectionName,
      'children': children?.map((child) => child.toJson()).toList(),
      'id': id, // Serialize the ID
    };
  }
}


class ApiFolder {
  String? folderName;
  String? requestData;
  String? method;
  String? url; // Add URL field
  Map<String, dynamic>? jsonData; // Add JSON data field
  Map<String, String>? headers; // Add headers field
  List<ApiFolder>? children;
  String id; // ID field

  ApiFolder({
    this.folderName,
    this.requestData,
    this.method,
    this.url,
    this.jsonData,
    this.headers, // Initialize headers
    this.children,
    required this.id, // ID is required
  }) {
    children ??= []; // Initialize children if null
  }

  factory ApiFolder.fromJson(Map<String, dynamic> json) {
    return ApiFolder(
      folderName: json['folderName'] as String?,
      requestData: json['requestData'] as String?,
      method: json['method'] as String?,
      url: json['url'] as String?,
      jsonData: json['jsonData'] as Map<String, dynamic>?,
      headers: json['headers'] != null
          ? Map<String, String>.from(json['headers'] as Map<dynamic, dynamic>) // Explicitly cast to Map<String, String>
          : null,
      children: (json['children'] as List<dynamic>?)
          ?.map((child) => ApiFolder.fromJson(child))
          .toList(),
      id: json['id'] as String, // Deserialize the ID
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'folderName': folderName,
      'requestData': requestData,
      'method': method,
      'url': url,
      'jsonData': jsonData,
      'headers': headers, // Serialize headers
      'children': children?.map((child) => child.toJson()).toList(),
      'id': id, // Serialize the ID
    };
  }
}



class TabModel {
  final String uuid; // Unique identifier for the tab
  final String tabName; // Name of the tab
  String method; // HTTP method (GET, POST, etc.)
  String url; // URL of the request
  Map<String, dynamic>? jsonData; // JSON data for the request
  Map<String, dynamic>? responseJsonData;
  final String? collectionName; // Associated collection name
  final String? folderName; // Associated folder name
  Map<String, String>? headers; // Headers map
  final bool isFromCollectionDialog; // Flag for _showAddRequestDialog
  final bool isFromFolderDialog;
  final bool isUpdateRequest;
  final bool isModified;

  TabModel({
    required this.uuid,
    required this.tabName,
    this.method = "GET",
    this.url = "",
    this.jsonData,
    this.responseJsonData,
    this.collectionName,
    this.folderName,
    this.headers,
    this.isFromCollectionDialog = false,
    this.isFromFolderDialog = false,
    this.isUpdateRequest = false,
    this.isModified = true,
  });

  // Convert the model to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'uuid': uuid,
      'tabName': tabName,
      'method': method,
      'url': url,
      'jsonData': jsonData,
      'responseJsonData': responseJsonData,
      'collectionName': collectionName,
      'folderName': folderName,
      'headers': headers,
    };
  }

  // Create a model from a JSON map
  factory TabModel.fromJson(Map<String, dynamic> json) {
    return TabModel(
      uuid: json['uuid'] as String,
      tabName: json['tabName'] as String,
      method: json['method'] as String,
      url: json['url'] as String,
      jsonData: json['jsonData'] as Map<String, dynamic>?,
      responseJsonData: json['responseJsonData'] as Map<String, dynamic>?,
      collectionName: json['collectionName'] as String?,
      folderName: json['folderName'] as String?,
      headers: json['headers'] as Map<String, String>?,
    );
  }

  // Add a copyWith method
  TabModel copyWith({
    String? tabName,
    String? method,
    String? url,
    Map<String, dynamic>? jsonData,
    Map<String, dynamic>? responseJsonData,
    String? collectionName,
    String? folderName,
    Map<String, String>? headers,
    bool? isFromCollectionDialog,
    bool? isFromFolderDialog,
    bool? isUpdateRequest,
    bool? isModified,
  }) {
    return TabModel(
      uuid: uuid,
      tabName: tabName ?? this.tabName,
      method: method ?? this.method,
      url: url ?? this.url,
      jsonData: jsonData ?? this.jsonData,
      responseJsonData: responseJsonData ?? this.responseJsonData,
      collectionName: collectionName ?? this.collectionName,
      folderName: folderName ?? this.folderName,
      headers: headers ?? this.headers,
      isFromCollectionDialog: isFromCollectionDialog ?? this.isFromCollectionDialog,
      isFromFolderDialog: isFromFolderDialog ?? this.isFromFolderDialog,
      isUpdateRequest: isUpdateRequest ?? this.isUpdateRequest,
      isModified: isModified ?? this.isModified,
    );
  }

  @override
  String toString() {
    return 'TabModel{uuid: $uuid, tabName: $tabName, method: $method, url: $url, jsonData: $jsonData, responseJsonData: $responseJsonData, collectionName: $collectionName, folderName: $folderName, headers: $headers}';
  }
}



class RequestHistoryModel {
  final String id;
  final String requestName;
  final String method;
  final String url;
  final Map<String, dynamic>? jsonData;
  final DateTime timestamp;

  RequestHistoryModel({
    required this.id,
    required this.requestName,
    required this.method,
    required this.url,
    this.jsonData,
    required this.timestamp,
  });

  factory RequestHistoryModel.fromJson(Map<String, dynamic> json) {
    return RequestHistoryModel(
      id: json['id'] as String,
      requestName: json['requestName'] as String,
      method: json['method'] as String,
      url: json['url'] as String,
      jsonData: json['jsonData'] as Map<String, dynamic>?,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'requestName': requestName,
      'method': method,
      'url': url,
      'jsonData': jsonData,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

