
import 'package:hive/hive.dart';

import '../tabs_views/header/bloc/headers_model.dart';

class ApiCollection {
  String? collectionName;
  List<ApiFolder>? children;
  String id; // Add the ID field

  ApiCollection({this.collectionName, this.children, required this.id});

  factory ApiCollection.fromJson(Map<String, dynamic> json) {
    return ApiCollection(
      collectionName: json['collectionName'] as String?,
      children: (json['children'] as List<dynamic>?)
          ?.map((child) => ApiFolder.fromJson(child))
          .toList(),
      id: json['id'] as String, // Deserialize the ID
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'collectionName': collectionName,
      'children': children?.map((child) => child.toJson()).toList(),
      'id': id, // Serialize the ID
    };
  }
}


class ApiFolder {
  String? folderName;
  String? requestData;
  String? method;
  String? url; // Add URL field
  Map<String, dynamic>? jsonData; // Add JSON data field
  Map<String, String>? headers; // Add headers field
  List<ApiFolder>? children;
  String id; // ID field

  ApiFolder({
    this.folderName,
    this.requestData,
    this.method,
    this.url,
    this.jsonData,
    this.headers, // Initialize headers
    this.children,
    required this.id, // ID is required
  }) {
    children ??= []; // Initialize children if null
  }

  factory ApiFolder.fromJson(Map<String, dynamic> json) {
    return ApiFolder(
      folderName: json['folderName'] as String?,
      requestData: json['requestData'] as String?,
      method: json['method'] as String?,
      url: json['url'] as String?,
      jsonData: json['jsonData'] as Map<String, dynamic>?,
      headers: json['headers'] != null
          ? Map<String, String>.from(json['headers'] as Map<dynamic, dynamic>) // Explicitly cast to Map<String, String>
          : null,
      children: (json['children'] as List<dynamic>?)
          ?.map((child) => ApiFolder.fromJson(child))
          .toList(),
      id: json['id'] as String, // Deserialize the ID
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'folderName': folderName,
      'requestData': requestData,
      'method': method,
      'url': url,
      'jsonData': jsonData,
      'headers': headers, // Serialize headers
      'children': children?.map((child) => child.toJson()).toList(),
      'id': id, // Serialize the ID
    };
  }
}



class TabModel {
  final String uuid; // Unique identifier for the tab
  final String tabName; // Name of the tab
  String method; // HTTP method (GET, POST, etc.)
  String url; // URL of the request
  Map<String, dynamic>? jsonData; // JSON data for the request
  Map<String, dynamic>? responseJsonData;
  final String? collectionName; // Associated collection name
  final String? folderName; // Associated folder name
  Map<String, String>? headers; // Headers map
  final bool isFromCollectionDialog; // Flag for _showAddRequestDialog
  final bool isFromFolderDialog;
  final bool isUpdateRequest;
  final bool isModified;

  // Original state for comparison
  final String? originalTabName;
  final String? originalMethod;
  final String? originalUrl;
  final Map<String, dynamic>? originalJsonData;
  final Map<String, String>? originalHeaders;

  TabModel({
    required this.uuid,
    required this.tabName,
    this.method = "GET",
    this.url = "",
    this.jsonData,
    this.responseJsonData,
    this.collectionName,
    this.folderName,
    this.headers,
    this.isFromCollectionDialog = false,
    this.isFromFolderDialog = false,
    this.isUpdateRequest = false,
    this.isModified = false,
    this.originalTabName,
    this.originalMethod,
    this.originalUrl,
    this.originalJsonData,
    this.originalHeaders,
  });

  // Convert the model to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'uuid': uuid,
      'tabName': tabName,
      'method': method,
      'url': url,
      'jsonData': jsonData,
      'responseJsonData': responseJsonData,
      'collectionName': collectionName,
      'folderName': folderName,
      'headers': headers,
    };
  }

  // Create a model from a JSON map
  factory TabModel.fromJson(Map<String, dynamic> json) {
    return TabModel(
      uuid: json['uuid'] as String,
      tabName: json['tabName'] as String,
      method: json['method'] as String,
      url: json['url'] as String,
      jsonData: json['jsonData'] as Map<String, dynamic>?,
      responseJsonData: json['responseJsonData'] as Map<String, dynamic>?,
      collectionName: json['collectionName'] as String?,
      folderName: json['folderName'] as String?,
      headers: json['headers'] as Map<String, String>?,
    );
  }

  // Add a copyWith method
  TabModel copyWith({
    String? tabName,
    String? method,
    String? url,
    Map<String, dynamic>? jsonData,
    Map<String, dynamic>? responseJsonData,
    String? collectionName,
    String? folderName,
    Map<String, String>? headers,
    bool? isFromCollectionDialog,
    bool? isFromFolderDialog,
    bool? isUpdateRequest,
    bool? isModified,
    String? originalTabName,
    String? originalMethod,
    String? originalUrl,
    Map<String, dynamic>? originalJsonData,
    Map<String, String>? originalHeaders,
  }) {
    return TabModel(
      uuid: uuid,
      tabName: tabName ?? this.tabName,
      method: method ?? this.method,
      url: url ?? this.url,
      jsonData: jsonData ?? this.jsonData,
      responseJsonData: responseJsonData ?? this.responseJsonData,
      collectionName: collectionName ?? this.collectionName,
      folderName: folderName ?? this.folderName,
      headers: headers ?? this.headers,
      isFromCollectionDialog: isFromCollectionDialog ?? this.isFromCollectionDialog,
      isFromFolderDialog: isFromFolderDialog ?? this.isFromFolderDialog,
      isUpdateRequest: isUpdateRequest ?? this.isUpdateRequest,
      isModified: isModified ?? this.isModified,
      originalTabName: originalTabName ?? this.originalTabName,
      originalMethod: originalMethod ?? this.originalMethod,
      originalUrl: originalUrl ?? this.originalUrl,
      originalJsonData: originalJsonData ?? this.originalJsonData,
      originalHeaders: originalHeaders ?? this.originalHeaders,
    );
  }

  /// Check if current state matches the original state
  bool isCurrentStateEqualToOriginal() {
    // If no original state is set, consider it as not modified
    if (originalTabName == null && originalMethod == null &&
        originalUrl == null && originalJsonData == null && originalHeaders == null) {
      return true;
    }

    // Compare each field with its original value
    final tabNameMatches = tabName == (originalTabName ?? tabName);
    final methodMatches = method == (originalMethod ?? method);
    final urlMatches = url == (originalUrl ?? url);
    final jsonDataMatches = _mapsEqual(jsonData, originalJsonData);
    final headersMatches = _mapsEqual(headers, originalHeaders);

    return tabNameMatches && methodMatches && urlMatches && jsonDataMatches && headersMatches;
  }

  /// Helper method to compare two maps for equality
  bool _mapsEqual<K, V>(Map<K, V>? map1, Map<K, V>? map2) {
    if (map1 == null && map2 == null) return true;
    if (map1 == null || map2 == null) return false;
    if (map1.length != map2.length) return false;

    for (final key in map1.keys) {
      if (!map2.containsKey(key) || map1[key] != map2[key]) {
        return false;
      }
    }
    return true;
  }

  /// Create a copy with original state set to current state
  TabModel withOriginalState() {
    return copyWith(
      originalTabName: tabName,
      originalMethod: method,
      originalUrl: url,
      originalJsonData: jsonData != null ? Map<String, dynamic>.from(jsonData!) : null,
      originalHeaders: headers != null ? Map<String, String>.from(headers!) : null,
    );
  }

  @override
  String toString() {
    return 'TabModel{uuid: $uuid, tabName: $tabName, method: $method, url: $url, jsonData: $jsonData, responseJsonData: $responseJsonData, collectionName: $collectionName, folderName: $folderName, headers: $headers}';
  }
}



class RequestHistoryModel {
  final String id;
  final String requestName;
  final String method;
  final String url;
  final Map<String, dynamic>? jsonData;
  final DateTime timestamp;

  RequestHistoryModel({
    required this.id,
    required this.requestName,
    required this.method,
    required this.url,
    this.jsonData,
    required this.timestamp,
  });

  factory RequestHistoryModel.fromJson(Map<String, dynamic> json) {
    return RequestHistoryModel(
      id: json['id'] as String,
      requestName: json['requestName'] as String,
      method: json['method'] as String,
      url: json['url'] as String,
      jsonData: json['jsonData'] as Map<String, dynamic>?,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'requestName': requestName,
      'method': method,
      'url': url,
      'jsonData': jsonData,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

