import 'dart:convert';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;


class LargeResponseView extends StatefulWidget {
  final Map<String, dynamic>? jsonData;
  final bool isDarkMode;
  final int maxLinesToRender;

  const LargeResponseView({
    super.key,
    required this.jsonData,
    this.isDarkMode = true,
    this.maxLinesToRender = 5000, // Limit the number of lines to render for very large responses
  });

  @override
  State<LargeResponseView> createState() => _LargeResponseViewState();
}

class _LargeResponseViewState extends State<LargeResponseView> {
  List<String> _lines = ['Loading...'];
  bool _isLoading = true;
  bool _isTruncated = false;
  final ScrollController _scrollController = ScrollController();

  static const double _itemExtent = 20.0; // Height of each line in pixels
  static const double _overscanCount = 50.0; // Number of items to render beyond visible area

  @override
  void initState() {
    super.initState();
    _processJsonInIsolate();
  }

  @override
  void didUpdateWidget(LargeResponseView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.jsonData != widget.jsonData) {
      setState(() {
        _isLoading = true;
        _lines = ['Loading...'];
      });
      _processJsonInIsolate();
    }
  }

  Future<void> _processJsonInIsolate() async {
    if (widget.jsonData == null) {
      setState(() {
        _isLoading = false;
        _lines = ['No data available'];
      });
      return;
    }

    try {
      if (kIsWeb) {
        await Future.microtask(() {
          try {
            // Format the JSON with indentation
            final jsonString = const JsonEncoder.withIndent('  ').convert(widget.jsonData!);
            final allLines = jsonString.split('\n');

            // Check if we need to truncate
            final isTruncated = allLines.length > widget.maxLinesToRender;
            final lines = isTruncated
                ? allLines.sublist(0, widget.maxLinesToRender)
                : allLines;

            if (mounted) {
              setState(() {
                _lines = lines;
                _isTruncated = isTruncated;
                _isLoading = false;
              });
            }
          } catch (e) {
            if (mounted) {
              setState(() {
                _isLoading = false;
                _lines = ['Error formatting JSON: $e'];
              });
            }
          }
        });
      } else {
        await _processWithIsolate();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _lines = ['Error processing JSON: $e'];
        });
      }
    }
  }

  Future<void> _processWithIsolate() async {
    await Future.microtask(() {
      try {
        final jsonString = const JsonEncoder.withIndent('  ').convert(widget.jsonData!);
        final allLines = jsonString.split('\n');

        // Check if we need to truncate
        final isTruncated = allLines.length > widget.maxLinesToRender;
        final lines = isTruncated
            ? allLines.sublist(0, widget.maxLinesToRender)
            : allLines;

        if (mounted) {
          setState(() {
            _lines = lines;
            _isTruncated = isTruncated;
            _isLoading = false;
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoading = false;
            _lines = ['Error formatting JSON: $e'];
          });
        }
      }
    });
  }


  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Column(
      children: [
        if (_isTruncated)
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              'Response is very large. Showing first ${widget.maxLinesToRender} lines.',
              style: TextStyle(
                color: widget.isDarkMode ? Colors.amber : Colors.orange,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        Expanded(
          child: Scrollbar(
            controller: _scrollController,
            child: ListView.builder(
              controller: _scrollController,
              itemCount: _lines.length,
              itemExtent: _itemExtent,
              cacheExtent: _itemExtent * _overscanCount,
              itemBuilder: (context, index) {
                if (index >= _lines.length) return const SizedBox.shrink();

                final line = _lines[index];

                return Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Line number
                    SizedBox(
                      width: 60,
                      child: Text(
                        '${index + 1}',
                        style: TextStyle(
                          color: widget.isDarkMode ? Colors.grey : Colors.grey.shade700,
                          fontFamily: 'monospace',
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                    const SizedBox(width: 8),
                    // Line content with syntax highlighting
                    Expanded(
                      child: Text(
                        line,
                        style: TextStyle(
                          color: _getColorForLine(line),
                          fontFamily: 'monospace',
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Color _getColorForLine(String line) {
    final trimmedLine = line.trim();

    if (widget.isDarkMode) {
      // Dark mode colors
      if (trimmedLine.startsWith('"') && trimmedLine.endsWith('",')) {
        return Colors.green; // String keys
      } else if (trimmedLine.contains('": "')) {
        return Colors.lightBlue; // String values
      } else if (trimmedLine.contains('": ') &&
                (trimmedLine.contains('true') || trimmedLine.contains('false'))) {
        return Colors.orange; // Boolean values
      } else if (trimmedLine.contains('": ') &&
                RegExp(r'": \d').hasMatch(trimmedLine)) {
        return Colors.purple.shade300; // Number values
      } else if (trimmedLine.contains('{') || trimmedLine.contains('}') ||
                trimmedLine.contains('[') || trimmedLine.contains(']')) {
        return Colors.white; // Brackets
      }
      return Colors.grey.shade300; // Default color
    } else {
      // Light mode colors
      if (trimmedLine.startsWith('"') && trimmedLine.endsWith('",')) {
        return Colors.green.shade800; // String keys
      } else if (trimmedLine.contains('": "')) {
        return Colors.blue.shade800; // String values
      } else if (trimmedLine.contains('": ') &&
                (trimmedLine.contains('true') || trimmedLine.contains('false'))) {
        return Colors.orange.shade800; // Boolean values
      } else if (trimmedLine.contains('": ') &&
                RegExp(r'": \d').hasMatch(trimmedLine)) {
        return Colors.purple.shade800; // Number values
      } else if (trimmedLine.contains('{') || trimmedLine.contains('}') ||
                trimmedLine.contains('[') || trimmedLine.contains(']')) {
        return Colors.black; // Brackets
      }
      return Colors.grey.shade800; // Default color
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}

