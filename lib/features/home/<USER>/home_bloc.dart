import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:hive/hive.dart';
import 'package:postman_flutter/features/home/<USER>/api_collection.dart';
import '../data/hive_db_provider.dart';
import '../tabs_views/header/bloc/headers_bloc.dart';
import '../tabs_views/header/bloc/headers_event.dart';
import '../tabs_views/header/bloc/headers_model.dart';
import 'home_event.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:uuid/uuid.dart';

import 'home_state.dart';


class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final HiveDBService _hiveDBService = HiveDBService();
  final Uuid uuid = Uuid();

  HomeBloc()
      : super(HomeState(apiData: [], openTabs: [], activeTab: null)) {
    // API Collection Events
    on<LoadApiDataEvent>(_onLoadApiData);
    on<AddCollectionEvent>(_onAddCollection);
    on<AddFolderEvent>(_onAddFolder);
    on<AddRequestEvent>(_onAddRequest);
    on<DeleteItemEvent>(_onDeleteItem);
    on<DeleteRequestEvent>(_onDeleteRequest);
    on<RenameRequestEvent>(_onRenameRequest);
    on<RenameFolderEvent>(_onRenameFolder);
    on<RenameCollectionEvent>(_onRenameCollection);
    on<DuplicateRequestEvent>(_onDuplicateRequest);
    on<DeleteFolderEvent>(_onDeleteFolder);
    on<SearchQueryChangedEvent>(_onSearchQueryChanged);
    on<SearchQueryChangedEventSaveRequest>(_onSearchQueryChangedSaveRequest);
    on<ToggleExpansionEvent>(_onToggleExpansion);
    on<ToggleForCollectionEvent>(_onToggleForCollection);
    // on<NavigateIntoFolderEvent>(_onNavigateIntoFolder);
    // on<NavigateOutOfFolderEvent>(_onNavigateOutOfFolder);
    on<SwitchToCollectionPanelEvent>(_onSwitchToCollectionPanel);
    on<SwitchToHistoryPanelEvent>(_onSwitchToHistoryPanel);
    on<SaveRequestToHistoryEvent>(_onSaveRequestToHistory);
    on<LoadRequestHistoryEvent>(_onLoadRequestHistory);
    on<DeleteRequestFromHistoryEvent>(_onDeleteRequestFromHistory);
    on<ClearRequestHistoryEvent>(_onClearRequestHistory);
    on<UpdateRequestEvent>(_onUpdateRequest);

    // Tab Management Events
    on<OpenTabEvent>(_onOpenTab);
    on<CloseTabEvent>(_onCloseTab);
    on<SwitchTabEvent>(_onSwitchTab);
    on<ChangeMethodEvent>(_onChangeMethod);
    on<ChangeUrlEvent>(_onChangeUrl);
    on<UpdateJsonDataEventTab>(_onUpdateJsonData);
    on<UpdateResponseJsonDataEventTab>(_onUpdateResponseJsonData);
    on<RemoveTabEvent>(_onRemoveTab);
    on<UpdateDialogTabEvent>(_onUpdateTab);

    on<UpdateHeadersEvent>((event, emit) {
      final updatedTabs = state.openTabs.map((tab) {
        if (tab.uuid == event.uuid) {
          return tab.copyWith(headers: event.headers); // Update headers for the tab
        }
        return tab;
      }).toList();

      emit(state.copyWith(openTabs: updatedTabs));
    });

  }

  (String?, String?) findCollectionAndFolderName(String requestId) {
    debugPrint('Searching for requestId: $requestId');
    for (final collection in state.apiData) {
      debugPrint('Checking collection: ${collection.collectionName}');

      try {
        // Check if the request is directly under the collection
        final request = collection.children?.firstWhere(
              (r) {
            debugPrint('Checking request: ${r.id}');
            return r.id == requestId;
          },
        );

        if (request != null) {
          debugPrint('Request found directly under collection: ${collection.collectionName}');
          return (collection.collectionName, null); // Request is directly under the collection
        }
      } catch (e) {
        debugPrint('Request not found directly under collection: ${collection.collectionName}');
      }

      // Check if the request is inside a folder
      for (final folder in collection.children ?? []) {
        if (folder.folderName != null) {
          debugPrint('Checking folder: ${folder.folderName}');
          try {
            final requestInFolder = folder.children?.firstWhere(
                  (r) {
                debugPrint('Checking request in folder: ${r.id}');
                return r.id == requestId;
              },
            );

            if (requestInFolder != null) {
              debugPrint('Request found in folder: ${folder.folderName}');
              return (collection.collectionName, folder.folderName); // Request is inside a folder
            }
          } catch (e) {
            debugPrint('Request not found in folder: ${folder.folderName}');
          }
        }
      }
    }

    debugPrint('Request not found: $requestId');
    return (null, null); // Request not found
  }

  Future<void> _onLoadApiData(LoadApiDataEvent event, Emitter<HomeState> emit) async {
    final apiData = await _hiveDBService.getApiCollections();
    emit(state.copyWith(apiData: apiData));
  }

  Future<void> _onAddCollection(AddCollectionEvent event, Emitter<HomeState> emit) async {
    final newCollection = ApiCollection(collectionName: event.collectionName, id: uuid.v4());
    await _hiveDBService.addCollection(newCollection);
    final apiData = await _hiveDBService.getApiCollections();
    emit(state.copyWith(apiData: apiData));
  }

  Future<void> _onAddFolder(AddFolderEvent event, Emitter<HomeState> emit) async {
    final collections = await _hiveDBService.getApiCollections();
    final collection = collections.firstWhere((c) => c.collectionName == event.collectionName, orElse: () => throw Exception('Collection not found'));
    final newFolder = ApiFolder(folderName: event.folderName, id: uuid.v4());
    collection.children ??= [];
    collection.children!.add(newFolder);
    await _hiveDBService.saveApiCollection(collections);
    emit(state.copyWith(apiData: collections));
  }

  Future<void> _onAddRequest(AddRequestEvent event, Emitter<HomeState> emit) async {
    final collections = await _hiveDBService.getApiCollections();
    final collection = collections.firstWhere(
          (c) => c.collectionName == event.collectionName,
      orElse: () => throw Exception('Collection not found'),
    );

    final newRequest = ApiFolder(
      requestData: event.requestName,
      method: event.method,
      url: event.url,
      jsonData: event.jsonData,
      id: Uuid().v4(),
    );

    if (event.folderName != null) {
      // Add the request to the specified folder
      final folder = _findFolderRecursive(collection.children, event.folderName!);
      if (folder != null) {
        folder.children ??= [];
        folder.children!.add(newRequest);
      } else {
        throw Exception('Folder not found');
      }
    } else {
      // Add the request directly under the collection
      collection.children ??= [];
      collection.children!.add(newRequest);
    }

    await _hiveDBService.saveApiCollection(collections);
    emit(state.copyWith(apiData: collections));
  }

  Future<void> _onDeleteItem(DeleteItemEvent event, Emitter<HomeState> emit) async {
    final collections = await _hiveDBService.getApiCollections();
    collections.removeWhere((c) => c.collectionName == event.collectionName);
    await _hiveDBService.saveApiCollection(collections);
    emit(state.copyWith(apiData: collections));
  }


  Future<void> _onDeleteRequest(DeleteRequestEvent event, Emitter<HomeState> emit) async {
    final collections = await _hiveDBService.getApiCollections();
    final collection = collections.firstWhere(
          (c) => c.collectionName == event.collectionName,
      orElse: () => throw Exception('Collection not found'),
    );

    if (event.folderName != null) {
      // Delete request inside a folder
      final folder = _findFolderRecursive(collection.children, event.folderName!);
      if (folder != null) {
        folder.children?.removeWhere((request) => request.id == event.requestId);
      }
    } else {
      // Delete request directly under the collection
      collection.children?.removeWhere((request) => request.id == event.requestId);
    }

    await _hiveDBService.saveApiCollection(collections);

    // Dispatch RemoveTabEvent to remove the corresponding tab
    add(RemoveTabEvent(requestId: event.requestId));

    emit(state.copyWith(apiData: collections));
  }

  void _onRemoveTab(RemoveTabEvent event, Emitter<HomeState> emit) {
    final updatedTabs = state.openTabs.where((tab) => tab.uuid != event.requestId).toList();
    final newActiveTab = state.activeTab?.uuid == event.requestId ? null : state.activeTab;

    emit(state.copyWith(openTabs: updatedTabs, activeTab: newActiveTab));
  }


  void _onUpdateTab(UpdateDialogTabEvent event, Emitter<HomeState> emit) {
    final updatedActiveTab = state.activeTab != null
        ? event.updatedTabs.firstWhere(
          (tab) => tab.uuid == state.activeTab!.uuid,
      orElse: () => state.activeTab!,
    )
        : null;

    emit(state.copyWith(openTabs: event.updatedTabs, activeTab: updatedActiveTab));
  }

  Future<void> _onRenameRequest(RenameRequestEvent event, Emitter<HomeState> emit) async {
    final collections = await _hiveDBService.getApiCollections();
    final collection = collections.firstWhere(
          (c) => c.collectionName == event.collectionName,
      orElse: () => throw Exception('Collection not found'),
    );

    if (event.folderName != null) {
      // Rename request inside a folder
      final folder = _findFolderRecursive(collection.children, event.folderName!);
      if (folder != null) {
        final request = folder.children?.firstWhere(
              (r) => r.id == event.requestId,
          orElse: () => throw Exception('Request not found'),
        );
        request?.requestData = event.newName;
      } else {
        throw Exception('Folder not found');
      }
    } else {
      // Rename request directly under the collection
      if (collection.children == null) {
        throw Exception('No requests found in the collection');
      }

      final request = collection.children!.firstWhere(
            (r) => r.id == event.requestId,
        orElse: () => throw Exception('Request not found'),
      );
      request.requestData = event.newName;
    }

    await _hiveDBService.saveApiCollection(collections);
    emit(state.copyWith(apiData: collections));
  }

  Future<void> _onRenameFolder(RenameFolderEvent event, Emitter<HomeState> emit) async {
    final collections = await _hiveDBService.getApiCollections();
    final collection = collections.firstWhere(
          (c) => c.collectionName == event.collectionName,
      orElse: () => throw Exception('Collection not found'),
    );

    final folder = _findFolderRecursive(collection.children, event.oldFolderName);
    if (folder != null) {
      folder.folderName = event.newFolderName;
      await _hiveDBService.saveApiCollection(collections);
      emit(state.copyWith(apiData: collections));
    } else {
      throw Exception('Folder not found');
    }
  }

  Future<void> _onRenameCollection(RenameCollectionEvent event, Emitter<HomeState> emit) async {
    final collections = await _hiveDBService.getApiCollections();
    final collection = collections.firstWhere(
          (c) => c.collectionName == event.oldCollectionName,
      orElse: () => throw Exception('Collection not found'),
    );

    collection.collectionName = event.newCollectionName;
    await _hiveDBService.saveApiCollection(collections);
    emit(state.copyWith(apiData: collections));
  }

  Future<void> _onDuplicateRequest(DuplicateRequestEvent event, Emitter<HomeState> emit) async {
    final collections = await _hiveDBService.getApiCollections();
    final collection = collections.firstWhere(
          (c) => c.collectionName == event.collectionName,
      orElse: () => throw Exception('Collection not found'),
    );

    ApiFolder? originalRequest;
    if (event.folderName != null) {
      // Find request inside a folder
      final folder = _findFolderRecursive(collection.children, event.folderName!);
      if (folder != null) {
        originalRequest = folder.children?.firstWhere(
              (r) => r.id == event.requestId,
          orElse: () => throw Exception('Request not found'),
        );
      }
    } else {
      // Find request directly under the collection
      originalRequest = collection.children?.firstWhere(
            (r) => r.id == event.requestId,
        orElse: () => throw Exception('Request not found'),
      );
    }

    if (originalRequest != null) {
      // Create a duplicate request
      final duplicatedRequest = ApiFolder(
        requestData: '${originalRequest.requestData} (Copy)',
        method: originalRequest.method,
        url: originalRequest.url,
        jsonData: originalRequest.jsonData,
        id: uuid.v4(),
      );

      if (event.folderName != null) {
        // Add duplicate to the same folder
        final folder = _findFolderRecursive(collection.children, event.folderName!);
        folder?.children?.add(duplicatedRequest);
      } else {
        // Add duplicate directly under the collection
        collection.children?.add(duplicatedRequest);
      }

      await _hiveDBService.saveApiCollection(collections);
      emit(state.copyWith(apiData: collections));
    }
  }

  Future<void> _onDeleteFolder(DeleteFolderEvent event, Emitter<HomeState> emit) async {
    final collections = await _hiveDBService.getApiCollections();
    final collection = collections.firstWhere(
          (c) => c.collectionName == event.collectionName,
      orElse: () => throw Exception('Collection not found'),
    );

    // Remove the folder
    collection.children?.removeWhere((folder) => folder.folderName == event.folderName);

    await _hiveDBService.saveApiCollection(collections);
    emit(state.copyWith(apiData: collections));
  }



  ApiFolder? _findFolderRecursive(List<ApiFolder>? folders, String folderName) {
    if (folders == null) return null;

    for (final folder in folders) {
      print("Checking folder: ${folder.folderName}");
      if (folder.folderName == folderName) {
        print("Folder found: ${folder.folderName}");
        return folder;
      }
      final found = _findFolderRecursive(folder.children, folderName);
      if (found != null) {
        return found;
      }
    }
    print("Folder not found: $folderName");
    return null;
  }

  void _onSearchQueryChanged(SearchQueryChangedEvent event, Emitter<HomeState> emit) {
    emit(state.copyWith(searchQuery: event.query));
  }

  void _onSearchQueryChangedSaveRequest(SearchQueryChangedEventSaveRequest event, Emitter<HomeState> emit) {
    emit(state.copyWith(searchQuerySaveRequest: event.querySaveReuest));
  }

  void _onToggleExpansion(ToggleExpansionEvent event, Emitter<HomeState> emit) {
    final expandedSections = Map<String, bool>.from(state.expandedSections);
    expandedSections[event.sectionId] = !(expandedSections[event.sectionId] ?? false);
    emit(state.copyWith(expandedSections: expandedSections));
  }


  void _onOpenTab(OpenTabEvent event, Emitter<HomeState> emit) {
    final headersBloc = HeadersBlocSingleton.headersBloc;

    final newTabs = List<TabModel>.from(state.openTabs);
    // Check if the tab already exists
    final existingTabIndex = newTabs.indexWhere((tab) => tab.uuid == event.tabModel.uuid);

    if (existingTabIndex != -1) {
      // Switch to the existing tab
      emit(state.copyWith(activeTab: newTabs[existingTabIndex]));
    } else {
      // Add the new tab and switch to it
      final newTab = event.tabModel.copyWith(responseJsonData: null); // Ensure responseJsonData is null
      newTabs.add(newTab);
      emit(state.copyWith(openTabs: newTabs, activeTab: newTab));

      if (newTab.headers != null) {
        final headersBloc = HeadersBlocSingleton.headersBloc; // Access HeadersBloc singleton
        headersBloc.add(LoadHeadersEvent(newTab.headers!));
      }

    }
  }

  void _onSwitchTab(SwitchTabEvent event, Emitter<HomeState> emit) {
    // Check if we're already on this tab to avoid unnecessary state updates
    if (state.activeTab?.uuid == event.uuid) {
      return; // Already on this tab, no need to update state
    }

    // Find the tab we want to switch to
    final newActiveTab = state.openTabs.firstWhere(
      (tab) => tab.uuid == event.uuid,
      orElse: () => throw Exception('Tab not found'),
    );

    // Update the state with the new active tab
    emit(state.copyWith(activeTab: newActiveTab));

    // Load headers for the new tab if available
    // Use Future.microtask to defer this operation until after the UI update
    if (newActiveTab.headers != null) {
      Future.microtask(() {
        final headersBloc = HeadersBlocSingleton.headersBloc;
        headersBloc.add(LoadHeadersEvent(newActiveTab.headers!));
      });
    }
  }


  void _onCloseTab(CloseTabEvent event, Emitter<HomeState> emit) {
    final newTabs = List<TabModel>.from(state.openTabs)
      ..removeWhere((tab) => tab.uuid == event.uuid);

    final newActiveTab = state.activeTab?.uuid == event.uuid
        ? (newTabs.isNotEmpty ? newTabs.last : null)
        : state.activeTab;

    emit(state.copyWith(openTabs: newTabs, activeTab: newActiveTab));
  }


  void _onChangeMethod(ChangeMethodEvent event, Emitter<HomeState> emit) {
    final updatedTabs = state.openTabs.map((tab) {
      if (tab.uuid == event.uuid) {
        return tab.copyWith(method: event.method, isModified: true);
      }
      return tab;
    }).toList();

    final updatedActiveTab = state.activeTab?.uuid == event.uuid
        ? updatedTabs.firstWhere((tab) => tab.uuid == event.uuid)
        : state.activeTab;

    emit(state.copyWith(openTabs: updatedTabs, activeTab: updatedActiveTab));
  }



  void _onChangeUrl(ChangeUrlEvent event, Emitter<HomeState> emit) {
    final updatedTabs = state.openTabs.map((tab) {
      if (tab.uuid == event.uuid) {
        return tab.copyWith(url: event.url, isModified: true);
      }
      return tab;
    }).toList();

    final updatedActiveTab = state.activeTab?.uuid == event.uuid
        ? updatedTabs.firstWhere((tab) => tab.uuid == event.uuid)
        : state.activeTab;

    emit(state.copyWith(openTabs: updatedTabs, activeTab: updatedActiveTab));
  }

  void _onUpdateJsonData(UpdateJsonDataEventTab event, Emitter<HomeState> emit) {
    if (state.activeTab != null) {
      // Create a new TabModel with the updated JSON data
      final updatedTab = state.activeTab!.copyWith(jsonData: event.jsonData,);

      // Update the list of open tabs
      final updatedTabs = state.openTabs.map((tab) =>
      tab.uuid == updatedTab.uuid ? updatedTab : tab).toList();

      // Emit the new state
      emit(state.copyWith(openTabs: updatedTabs, activeTab: updatedTab));
    }
  }

  void _onUpdateResponseJsonData(UpdateResponseJsonDataEventTab event, Emitter<HomeState> emit) {
    if (state.activeTab != null) {
      final currentResponseJsonData = state.activeTab!.responseJsonData;

      // Only update if the response data has changed
      if (currentResponseJsonData != event.responseJsonData) {
        // Use a more efficient approach for large data updates
        // Create a new tab with the updated response data
        final updatedTab = state.activeTab!.copyWith(responseJsonData: event.responseJsonData);

        // Update only the active tab in the list to minimize state changes
        final updatedTabs = List<TabModel>.from(state.openTabs);
        final activeTabIndex = updatedTabs.indexWhere((tab) => tab.uuid == updatedTab.uuid);
        if (activeTabIndex != -1) {
          updatedTabs[activeTabIndex] = updatedTab;
        }

        // Emit the new state
        emit(state.copyWith(openTabs: updatedTabs, activeTab: updatedTab));
      }
    }
  }

  void _onToggleForCollection(ToggleForCollectionEvent event, Emitter<HomeState> emit) {
    emit(state.copyWith(forCollection: event.forCollection));
  }

  void _onSwitchToCollectionPanel(SwitchToCollectionPanelEvent event, Emitter<HomeState> emit) {
    print('Hit 1');
    emit(state.copyWith(leftPanelType: LeftPanelType.collection));
  }

  void _onSwitchToHistoryPanel(SwitchToHistoryPanelEvent event, Emitter<HomeState> emit) {
    emit(state.copyWith(leftPanelType: LeftPanelType.history));
  }

  void _onSaveRequestToHistory(SaveRequestToHistoryEvent event, Emitter<HomeState> emit) async {
    final request = RequestHistoryModel(
      id: uuid.v4(),
      requestName: event.requestName,
      method: event.method,
      url: event.url,
      jsonData: event.jsonData,
      timestamp: DateTime.now(),
    );

    await _hiveDBService.saveRequestToHistory(request);

    // Load updated history
    final history = await _hiveDBService.getRequestHistory();
    emit(state.copyWith(requestHistory: history)); // Update the state with new history
  }

  void _onLoadRequestHistory(LoadRequestHistoryEvent event, Emitter<HomeState> emit) async {
    final history = await _hiveDBService.getRequestHistory();
    emit(state.copyWith(requestHistory: history)); // Update the state with loaded history
  }

  void _onDeleteRequestFromHistory(DeleteRequestFromHistoryEvent event, Emitter<HomeState> emit) async {
    await _hiveDBService.deleteRequestFromHistory(event.id);

    // Load updated history
    final history = await _hiveDBService.getRequestHistory();
    emit(state.copyWith(requestHistory: history)); // Update the state after deletion
  }

  void _onClearRequestHistory(ClearRequestHistoryEvent event, Emitter<HomeState> emit) async {
    await _hiveDBService.clearRequestHistory();
    emit(state.copyWith(requestHistory: [])); // Clear the history in the state
  }

  Future<void> _onUpdateRequest(UpdateRequestEvent event, Emitter<HomeState> emit) async {
    final collections = await _hiveDBService.getApiCollections();
    final collection = collections.firstWhere(
          (c) => c.collectionName == event.collectionName,
      orElse: () => throw Exception('Collection not found: ${event.collectionName}'),
    );

    ApiFolder? requestToUpdate;

    if (event.folderName != null) {
      // Find the request inside the folder
      final folder = _findFolderRecursive(collection.children, event.folderName!);
      if (folder != null) {
        requestToUpdate = folder.children?.firstWhere(
              (r) => r.id == event.requestId,
          orElse: () => throw Exception('Request not found: ${event.requestId}'),
        );
      }
    } else {
      // Find the request directly under the collection
      requestToUpdate = collection.children?.firstWhere(
            (r) => r.id == event.requestId,
        orElse: () => throw Exception('Request not found: ${event.requestId}'),
      );
    }

    if (requestToUpdate != null) {
      // Update the request
      requestToUpdate.requestData = event.requestName;
      requestToUpdate.method = event.method;
      requestToUpdate.url = event.url;
      requestToUpdate.jsonData = event.jsonData;
    }

    await _hiveDBService.saveApiCollection(collections);
    emit(state.copyWith(apiData: collections));
  }

}




