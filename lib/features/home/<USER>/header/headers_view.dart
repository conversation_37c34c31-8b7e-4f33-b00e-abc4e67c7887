import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:async';
import '../../../../helpers/color_config.dart';
import '../../../../helpers/style_config.dart';
import '../../../common_widgets/CommonText.dart';
import '../../../common_widgets/custom_checkbox.dart';
import '../../bloc/home_bloc.dart';
import '../../bloc/home_event.dart';
import '../../bloc/home_state.dart';
import 'bloc/headers_bloc.dart';
import 'bloc/headers_event.dart';
import 'bloc/headers_state.dart';

class HeadersView extends StatefulWidget {
  final String uuid;

  const HeadersView({
    required this.uuid, // Require uuid
    super.key,
  });

  @override
  State<HeadersView> createState() => _HeadersViewState();
}

class _HeadersViewState extends State<HeadersView> {
  int? hoveredColumnIndex;
  bool isDragging = false;
  List<double> columnWidths = [1.0, 1.0, 2.0];
  bool _isInitialized = false;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();

    // Initialize headers for this tab only once
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_isInitialized) {
        _isInitialized = true;
        final headersBloc = context.read<HeadersBloc>();

        // Load headers from the active tab if available
        final activeTab = context.read<HomeBloc>().state.activeTab;
        if (activeTab != null && activeTab.uuid == widget.uuid) {
          if (activeTab.headers != null && activeTab.headers!.isNotEmpty) {
            headersBloc.add(LoadHeadersEvent(activeTab.headers!, tabUuid: widget.uuid));
          } else {
            debugPrint('HeadersView: Initializing empty headers for ${widget.uuid}');
            headersBloc.add(InitializeHeadersForTabEvent(tabUuid: widget.uuid));
          }
        } else {
          debugPrint('HeadersView: No active tab found, initializing empty headers for ${widget.uuid}');
          headersBloc.add(InitializeHeadersForTabEvent(tabUuid: widget.uuid));
        }
      }
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  // Debounce function to prevent too many updates
  void _debouncedUpdateTabHeaders(BuildContext context) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      final homeBloc = context.read<HomeBloc>();
      final activeTab = homeBloc.state.activeTab;
      final headersBloc = context.read<HeadersBloc>();

      if (activeTab != null && activeTab.uuid == widget.uuid) {
        final updatedHeaders = headersBloc.getHeadersMapForTab(widget.uuid);
        homeBloc.add(UpdateHeadersEvent(uuid: activeTab.uuid, headers: updatedHeaders));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) {
        // Only rebuild if the active tab changes to or from this tab
        if (previous.activeTab?.uuid != current.activeTab?.uuid) {
          return previous.activeTab?.uuid == widget.uuid || current.activeTab?.uuid == widget.uuid;
        }
        return false;
      },
      builder: (context, homeState) {
        return BlocBuilder<HeadersBloc, HeadersState>(
          buildWhen: (previous, current) {
            // Only rebuild if this is the current tab's state and rows changed
            if (current.tabUuid != widget.uuid) return false;

            // Check if rows actually changed (avoid rebuilding for the same data)
            if (previous.rows.length != current.rows.length) return true;

            // Don't rebuild if only the internal state of TextEditingControllers changed
            return false;
          },
          builder: (context, state) {
            // If the state doesn't match this tab, initialize it if not already done
            if (state.tabUuid != widget.uuid && !_isInitialized) {
              _isInitialized = true;

              // Get the bloc reference before the async gap
              final headersBloc = context.read<HeadersBloc>();

              // Use a microtask to avoid rebuilding during build
              Future.microtask(() {
                if (mounted) {
                  headersBloc.add(InitializeHeadersForTabEvent(tabUuid: widget.uuid));
                }
              });

              // Show a loading indicator
              return const Center(child: CircularProgressIndicator());
            }

            // Get the rows for this tab
            final rows = context.read<HeadersBloc>().getHeadersForTab(widget.uuid);

            return Padding(
              padding: const EdgeInsets.only(right: 20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.only(bottom: 10),
                    child: CommonText(
                      text: "Headers",
                      textStyle: mMediumTextStyle16(
                          textSize: 14, textColor: AppThemeColor.white),
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.vertical,
                      child: Table(
                        border: TableBorder.all(
                            color: AppThemeColor.commonBorderColor),
                        columnWidths: {
                          0: const FixedColumnWidth(40),
                          1: FlexColumnWidth(columnWidths[0]),
                          2: FlexColumnWidth(columnWidths[1]),
                          3: FlexColumnWidth(columnWidths[2]),
                        },
                        defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                        children: [
                          _buildHeaderRow(),
                          ...List.generate(rows.length, (index) {
                            final row = rows[index];
                            return TableRow(
                              children: [
                                _buildCheckboxCell(context, index, row.isSelected),
                                _buildTextFieldCell(context, index, row.keyController, 'key', isStatic: row.isStatic),
                                _buildTextFieldCell(context, index, row.valueController, 'value'),
                                _buildTextFieldCell(context, index, row.descriptionController, 'description'),
                              ],
                            );
                          }),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  TableRow _buildHeaderRow() {
    return TableRow(
      children: [
        Container(),
        _buildResizableHeaderCell('Key', 0),
        _buildResizableHeaderCell('Value', 1),
        _buildResizableHeaderCell('Description', 2),
      ],
    );
  }

  Widget _buildResizableHeaderCell(String text, int columnIndex) {
    return MouseRegion(
      onEnter: (_) {
        setState(() {
          hoveredColumnIndex = columnIndex;
        });
      },
      onExit: (_) {
        setState(() {
          hoveredColumnIndex = null;
        });
      },
      child: Stack(
        children: [
          Container(
            height: 35,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            color: AppThemeColor.commonBackground,
            child: CommonText(
              text: text,
              textAlign: TextAlign.left,
              textStyle: mMediumTextStyle16(
                  textSize: 14, textColor: AppThemeColor.tableHeaderTextColor),
            ),
          ),
          if (hoveredColumnIndex == columnIndex || isDragging)
            Positioned(
              right: -10,
              top: 0,
              bottom: 0,
              child: GestureDetector(
                onHorizontalDragStart: (_) {
                  setState(() {
                    isDragging = true;
                  });
                },
                onHorizontalDragUpdate: (details) {
                  setState(() {
                    columnWidths[columnIndex] += details.primaryDelta! * 0.01;
                    columnWidths[columnIndex] =
                        columnWidths[columnIndex].clamp(0.5, 5.0);
                  });
                },
                onHorizontalDragEnd: (_) {
                  setState(() {
                    isDragging = false;
                  });
                },
                child: MouseRegion(
                  cursor: SystemMouseCursors.resizeColumn,
                  child: Container(
                    width: 20,
                    alignment: Alignment.center,
                    child: Icon(
                      Icons.drag_handle,
                      size: 16,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTextFieldCell(
    BuildContext context,
    int rowIndex,
    TextEditingController controller,
    String fieldName, {
    bool isStatic = false,
  }) {
    return Container(
      height: 35,
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      alignment: Alignment.centerLeft,
      child: TextField(
        controller: controller,
        textAlign: TextAlign.left,
        style: const TextStyle(
            color: AppThemeColor.tableHeaderTextColor, fontSize: 12),
        decoration: InputDecoration(
          contentPadding: EdgeInsets.zero,
          border: const OutlineInputBorder(
            borderSide: BorderSide.none,
          ),
          hintText: fieldName,
          hintStyle: const TextStyle(
            color: AppThemeColor.tableTextColor, fontSize: 12
          ),
        ),
        onChanged: (value) {
          // Don't rebuild the UI, just update the controller and model
          final headersBloc = context.read<HeadersBloc>();

          // Update the model without triggering a UI rebuild
          headersBloc.add(
            UpdateRowEvent(
              rowIndex: rowIndex,
              key: fieldName == 'key' ? value : controller.text,
              value: fieldName == 'value' ? value : controller.text,
              description: fieldName == 'description' ? value : controller.text,
              tabUuid: widget.uuid,
            ),
          );

          // Debounce the update to HomeBloc to prevent too many rebuilds
          _debouncedUpdateTabHeaders(context);
        },
      ),
    );
  }

  Widget _buildCheckboxCell(BuildContext context, int rowIndex, bool isSelected) {
    return Center(
      child: CustomCheckbox(
        value: isSelected,
        label: "",
        checkboxSize: 20,
        checkIconSize: 14,
        onChanged: (value) {
          context.read<HeadersBloc>().add(
            ToggleRowSelectionEvent(
              rowIndex: rowIndex,
              isSelected: value,
              tabUuid: widget.uuid,
            ),
          );

          // Debounce the update to HomeBloc
          _debouncedUpdateTabHeaders(context);
        },
      ),
    );
  }
}
