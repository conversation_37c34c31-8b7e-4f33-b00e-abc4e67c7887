import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../helpers/color_config.dart';
import '../../../../helpers/style_config.dart';
import '../../../common_widgets/CommonText.dart';
import '../../../common_widgets/custom_checkbox.dart';
import '../../bloc/home_bloc.dart';
import '../../bloc/home_event.dart';
import '../../bloc/home_state.dart';
import 'bloc/headers_bloc.dart';
import 'bloc/headers_event.dart';
import 'bloc/headers_model.dart';
import 'bloc/headers_state.dart';


//old tested
class HeadersView extends StatefulWidget {

  final String uuid;

  const HeadersView({
    required this.uuid, // Require uuid
    Key? key,
  }) : super(key: key);

  @override
  State<HeadersView> createState() => _HeadersViewState();
}

class _HeadersViewState extends State<HeadersView> {
  int? hoveredColumnIndex;
  bool isDragging = false;
  List<double> columnWidths = [1.0, 1.0, 2.0];
  late HeadersBloc headersBloc;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    // headersBloc = BlocProvider.of<HeadersBloc>(context,listen: false);
    // WidgetsBinding.instance.addPostFrameCallback((_){
    //   headersBloc.add(LoadHeadersEvent());
    // });
  }



  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, homeState) {

        final activeTab = homeState.activeTab;
        final headersBloc = context.read<HeadersBloc>();

        // Load headers data when the tab changes
        if (activeTab != null && activeTab.uuid == widget.uuid && activeTab.headers != null) {
          headersBloc.add(LoadHeadersEvent(activeTab.headers!));
        }

        return BlocBuilder<HeadersBloc, HeadersState>(
          builder: (context, state) {
            return Padding(
              padding: const EdgeInsets.only(right: 20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.only(bottom: 10),
                    child: CommonText(
                      text: "Headers",
                      textStyle: mMediumTextStyle16(
                          textSize: 14, textColor: AppThemeColor.white),
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.vertical,
                      child: Table(
                        border: TableBorder.all(
                            color: AppThemeColor.commonBorderColor),
                        columnWidths: {
                          0: const FixedColumnWidth(40),
                          1: FlexColumnWidth(columnWidths[0]),
                          2: FlexColumnWidth(columnWidths[1]),
                          3: FlexColumnWidth(columnWidths[2]),
                        },
                        defaultVerticalAlignment: TableCellVerticalAlignment
                            .middle,
                        children: [
                          _buildHeaderRow(),
                          ..._buildDataRows(context, state),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  TableRow _buildHeaderRow() {
    return TableRow(
      children: [
        Container(),
        _buildResizableHeaderCell('Key', 0),
        _buildResizableHeaderCell('Value', 1),
        _buildResizableHeaderCell('Description', 2),
      ],
    );
  }

  Widget _buildResizableHeaderCell(String text, int columnIndex) {
    return MouseRegion(
      onEnter: (_) {
        setState(() {
          hoveredColumnIndex = columnIndex;
        });
      },
      onExit: (_) {
        setState(() {
          hoveredColumnIndex = null;
        });
      },
      child: Stack(
        children: [
          Container(
            height: 35,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            color: AppThemeColor.commonBackground,
            child: CommonText(
              text: text,
              textAlign: TextAlign.left,
              textStyle: mMediumTextStyle16(
                  textSize: 14, textColor: AppThemeColor.tableHeaderTextColor),
            ),
          ),
          if (hoveredColumnIndex == columnIndex || isDragging)
            Positioned(
              right: -10,
              top: 0,
              bottom: 0,
              child: GestureDetector(
                onHorizontalDragStart: (_) {
                  setState(() {
                    isDragging = true;
                  });
                },
                onHorizontalDragUpdate: (details) {
                  setState(() {
                    columnWidths[columnIndex] += details.primaryDelta! * 0.01;
                    columnWidths[columnIndex] =
                        columnWidths[columnIndex].clamp(0.5, 5.0);
                  });
                },
                onHorizontalDragEnd: (_) {
                  setState(() {
                    isDragging = false;
                  });
                },
                child: MouseRegion(
                  cursor: SystemMouseCursors.resizeColumn,
                  child: Container(
                    width: 20,
                    alignment: Alignment.center,
                    child: Icon(
                      Icons.drag_handle,
                      size: 16,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }


  List<TableRow> _buildDataRows(BuildContext context, HeadersState state) {
    return List.generate(state.rows.length, (index) {
      final row = state.rows[index];
      return TableRow(
        children: [
          _buildCheckboxCell(context, index, row.isSelected),
          _buildTextFieldCell(
              context, index, row.keyController, 'key', isStatic: row.isStatic),
          _buildTextFieldCell(context, index, row.valueController, 'value'),
          _buildTextFieldCell(
              context, index, row.descriptionController, 'description'),
        ],
      );
    });
  }

  Widget _buildTextFieldCell(BuildContext context,
      int rowIndex,
      TextEditingController controller,
      String fieldName, {
        bool isStatic = false,
      }) {
    return Container(
      height: 35,
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      alignment: Alignment.centerLeft,
      child: TextField(
        controller: controller,
        textAlign: TextAlign.left,
        style: TextStyle(
            color: AppThemeColor.tableHeaderTextColor, fontSize: 12),
        decoration: InputDecoration(
          contentPadding: EdgeInsets.zero,
          border: const OutlineInputBorder(
            borderSide: BorderSide.none,
          ),
          hintText: fieldName,
          hintStyle: TextStyle(
              color: AppThemeColor.tableTextColor, fontSize: 12),
        ),
        onChanged: (value) {
          final headersBloc = BlocProvider.of<HeadersBloc>(context);
          final row = headersBloc.state.rows[rowIndex];

          headersBloc.add(
            UpdateRowEvent(
              rowIndex: rowIndex,
              key: fieldName == 'key' ? value : row.key,
              value: fieldName == 'value' ? value : row.value,
              description: fieldName == 'description' ? value : row.description,
            ),
          );

        },
      ),
    );
  }


  Widget _buildCheckboxCell(BuildContext context, int rowIndex,
      bool isSelected) {
    return Center(
      child: CustomCheckbox(
        value: isSelected,
        label: "",
        checkboxSize: 20,
        checkIconSize: 14,
        onChanged: (value) {
          BlocProvider.of<HeadersBloc>(context).add(
            ToggleRowSelectionEvent(rowIndex: rowIndex, isSelected: value!),
          );

          final homeBloc = context.read<HomeBloc>();
          final activeTab = homeBloc.state.activeTab;
          if (activeTab != null) {
            final updatedHeaders = context.read<HeadersBloc>().state.rows
                .where((row) => row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty)
                .fold<Map<String, String>>({}, (map, row) {
              map[row.key] = row.value;
              return map;
            });

            homeBloc.add(UpdateHeadersEvent(uuid: activeTab.uuid, headers: updatedHeaders));
          }
        },
      ),
    );
  }

}


/*class HeadersView extends StatefulWidget {
  const HeadersView({super.key});

  @override
  State<HeadersView> createState() => _HeadersViewState();
}

class _HeadersViewState extends State<HeadersView> {
  int? hoveredColumnIndex;
  bool isDragging = false;
  List<double> columnWidths = [1.0, 1.0, 2.0];

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, homeState) {

        final activeTab = homeState.activeTab;
        final headersBloc = context.read<HeadersBloc>();

        // Load headers data when the tab changes
        if (activeTab != null && activeTab.headers != null) {
          headersBloc.add(LoadHeadersEvent(activeTab.headers!));
        }

        return BlocBuilder<HeadersBloc, HeadersState>(
          builder: (context, headersState) {
            return Padding(
              padding: const EdgeInsets.only(right: 20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.only(bottom: 10),
                    child: CommonText(
                      text: "Headers",
                      textStyle: mMediumTextStyle16(
                          textSize: 14, textColor: AppThemeColor.white),
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.vertical,
                      child: Table(
                        border: TableBorder.all(
                            color: AppThemeColor.commonBorderColor),
                        columnWidths: {
                          0: const FixedColumnWidth(40),
                          1: FlexColumnWidth(columnWidths[0]),
                          2: FlexColumnWidth(columnWidths[1]),
                          3: FlexColumnWidth(columnWidths[2]),
                        },
                        defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                        children: [
                          _buildHeaderRow(),
                          ..._buildDataRows(context, headersState),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  TableRow _buildHeaderRow() {
    return TableRow(
      children: [
        Container(),
        _buildResizableHeaderCell('Key', 0),
        _buildResizableHeaderCell('Value', 1),
        _buildResizableHeaderCell('Description', 2),
      ],
    );
  }

  Widget _buildResizableHeaderCell(String text, int columnIndex) {
    return MouseRegion(
      onEnter: (_) {
        setState(() {
          hoveredColumnIndex = columnIndex;
        });
      },
      onExit: (_) {
        setState(() {
          hoveredColumnIndex = null;
        });
      },
      child: Stack(
        children: [
          Container(
            height: 35,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            color: AppThemeColor.commonBackground,
            child: CommonText(
              text: text,
              textAlign: TextAlign.left,
              textStyle: mMediumTextStyle16(
                  textSize: 14, textColor: AppThemeColor.tableHeaderTextColor),
            ),
          ),
          if (hoveredColumnIndex == columnIndex || isDragging)
            Positioned(
              right: -10,
              top: 0,
              bottom: 0,
              child: GestureDetector(
                onHorizontalDragStart: (_) {
                  setState(() {
                    isDragging = true;
                  });
                },
                onHorizontalDragUpdate: (details) {
                  setState(() {
                    columnWidths[columnIndex] += details.primaryDelta! * 0.01;
                    columnWidths[columnIndex] =
                        columnWidths[columnIndex].clamp(0.5, 5.0);
                  });
                },
                onHorizontalDragEnd: (_) {
                  setState(() {
                    isDragging = false;
                  });
                },
                child: MouseRegion(
                  cursor: SystemMouseCursors.resizeColumn,
                  child: Container(
                    width: 20,
                    alignment: Alignment.center,
                    child: Icon(
                      Icons.drag_handle,
                      size: 16,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  List<TableRow> _buildDataRows(BuildContext context, HeadersState state) {
    return List.generate(state.rows.length, (index) {
      final row = state.rows[index];
      return TableRow(
        children: [
          _buildCheckboxCell(context, index, row.isSelected),
          _buildTextFieldCell(
              context, index, row.keyController, 'key', isStatic: row.isStatic),
          _buildTextFieldCell(context, index, row.valueController, 'value'),
          _buildTextFieldCell(
              context, index, row.descriptionController, 'description'),
        ],
      );
    });
  }

  Widget _buildTextFieldCell(BuildContext context,
      int rowIndex,
      TextEditingController controller,
      String fieldName, {
        bool isStatic = false,
      }) {
    return Container(
      height: 35,
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      alignment: Alignment.centerLeft,
      child: TextField(
        controller: controller,
        textAlign: TextAlign.left,
        style: TextStyle(
            color: AppThemeColor.tableHeaderTextColor, fontSize: 12),
        decoration: InputDecoration(
          contentPadding: EdgeInsets.zero,
          border: const OutlineInputBorder(
            borderSide: BorderSide.none,
          ),
          hintText: fieldName,
          hintStyle: TextStyle(
              color: AppThemeColor.tableTextColor, fontSize: 12),
        ),
        onChanged: (value) {
          final headersBloc = BlocProvider.of<HeadersBloc>(context);
          final row = headersBloc.state.rows[rowIndex];

          headersBloc.add(
            UpdateRowEvent(
              rowIndex: rowIndex,
              key: fieldName == 'key' ? value : row.key,
              value: fieldName == 'value' ? value : row.value,
              description: fieldName == 'description' ? value : row.description,
            ),
          );

          // Update the active tab's headers
          final homeBloc = context.read<HomeBloc>();
          final activeTab = homeBloc.state.activeTab;
          if (activeTab != null) {
            final updatedHeaders = headersBloc.state.rows
                .where((row) => row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty)
                .fold<Map<String, String>>({}, (map, row) {
              map[row.key] = row.value;
              return map;
            });

            homeBloc.add(UpdateHeadersEvent(uuid: activeTab.uuid, headers: updatedHeaders));
          }
        },
      ),
    );
  }

  Widget _buildCheckboxCell(BuildContext context, int rowIndex,
      bool isSelected) {
    return Center(
      child: CustomCheckbox(
        value: isSelected,
        label: "",
        checkboxSize: 20,
        checkIconSize: 14,
        onChanged: (value) {
          BlocProvider.of<HeadersBloc>(context).add(
            ToggleRowSelectionEvent(rowIndex: rowIndex, isSelected: value!),
          );

          // Update the active tab's headers
          final homeBloc = context.read<HomeBloc>();
          final activeTab = homeBloc.state.activeTab;
          if (activeTab != null) {
            final updatedHeaders = context.read<HeadersBloc>().state.rows
                .where((row) => row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty)
                .fold<Map<String, String>>({}, (map, row) {
              map[row.key] = row.value;
              return map;
            });

            homeBloc.add(UpdateHeadersEvent(uuid: activeTab.uuid, headers: updatedHeaders));
          }
        },
      ),
    );
  }
}*/







