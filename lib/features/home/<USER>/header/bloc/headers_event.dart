
import 'package:equatable/equatable.dart';

import 'headers_model.dart';

abstract class HeadersEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class AddRowEvent extends HeadersEvent {}

class UpdateRowEvent extends HeadersEvent {
  final int rowIndex;
  final String key;
  final String value;
  final String description;

  UpdateRowEvent({
    required this.rowIndex,
    required this.key,
    required this.value,
    required this.description,
  });

  @override
  List<Object> get props => [rowIndex, key, value, description];
}

class ToggleRowSelectionEvent extends HeadersEvent {
  final int rowIndex;
  final bool isSelected;

  ToggleRowSelectionEvent({required this.rowIndex, required this.isSelected});

  @override
  List<Object> get props => [rowIndex, isSelected];
}

class LoadHeadersEvent extends HeadersEvent {
  final Map<String, String> headers;

  LoadHeadersEvent(this.headers);

  @override
  List<Object> get props => [headers];
}

class ResetHeadersEvent extends HeadersEvent {
  @override
  List<Object> get props => [];
}


// Bloc: Handles the logic for adding/updating rows





