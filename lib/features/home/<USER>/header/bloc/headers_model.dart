import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';



/*class HeaderRow {
  final TextEditingController keyController;
  final TextEditingController valueController;
  final TextEditingController descriptionController;
  String key;
  String value;
  String description;
  bool isSelected;
  final bool isStatic;

  HeaderRow({
    required this.keyController,
    required this.valueController,
    required this.descriptionController,
    this.key = '',
    this.value = '',
    this.description = '',
    this.isSelected = true, // Default to checked
    this.isStatic = false, // Indicates if the row is static
  });

  HeaderRow copyWith({
    String? key,
    String? value,
    String? description,
    bool? isSelected,
    bool? isStatic,
  }) {
    return HeaderRow(
      keyController: this.keyController,
      valueController: this.valueController,
      descriptionController: this.descriptionController,
      key: key ?? this.key,
      value: value ?? this.value,
      description: description ?? this.description,
      isSelected: isSelected ?? this.isSelected,
      isStatic: isStatic ?? this.isStatic,
    );
  }
}*/

class HeaderRow {
  final TextEditingController keyController;
  final TextEditingController valueController;
  final TextEditingController descriptionController;
  String key;
  String value;
  String description;
  bool isSelected;
  final bool isStatic;

  HeaderRow({
    required this.keyController,
    required this.valueController,
    required this.descriptionController,
    this.key = '',
    this.value = '',
    this.description = '',
    this.isSelected = true, // Default to checked
    this.isStatic = false, // Indicates if the row is static
  });

  HeaderRow copyWith({
    String? key,
    String? value,
    String? description,
    bool? isSelected,
    bool? isStatic,
  }) {
    return HeaderRow(
      keyController: this.keyController,
      valueController: this.valueController,
      descriptionController: this.descriptionController,
      key: key ?? this.key,
      value: value ?? this.value,
      description: description ?? this.description,
      isSelected: isSelected ?? this.isSelected,
      isStatic: isStatic ?? this.isStatic,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'value': value,
      'description': description,
      'isSelected': isSelected,
      'isStatic': isStatic,
    };
  }

  // Create from JSON
  factory HeaderRow.fromJson(Map<String, dynamic> json) {
    return HeaderRow(
      keyController: TextEditingController(text: json['key']),
      valueController: TextEditingController(text: json['value']),
      descriptionController: TextEditingController(text: json['description']),
      key: json['key'] as String,
      value: json['value'] as String,
      description: json['description'] as String,
      isSelected: json['isSelected'] as bool,
      isStatic: json['isStatic'] as bool,
    );
  }
}












