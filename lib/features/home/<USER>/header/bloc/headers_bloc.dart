import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';

import 'headers_event.dart';
import 'headers_model.dart';
import 'headers_state.dart';


class HeadersBloc extends Bloc<HeadersEvent, HeadersState> {
  HeadersBloc()
      : super(HeadersState(
      rows: [
    HeaderRow(
      keyController: TextEditingController(text: "Content-Type"),
      valueController: TextEditingController(text: "application/json"),
      descriptionController: TextEditingController(),
      key: "Content-Type",
      value: "application/json",
      isStatic: false,
    ),
    HeaderRow(
      keyController: TextEditingController(text: "Authorization"),
      valueController: TextEditingController(text: "Bearer <token>"),
      descriptionController: TextEditingController(),
      key: "Authorization",
      value: "Bearer <token>",
      isStatic: false,
      ),
        HeaderRow(
          keyController: TextEditingController(text: ""),
          valueController: TextEditingController(text: ""),
          descriptionController: TextEditingController(),
          key: "",
          value: "",
          isStatic: false,
        ),

  ])
  ) {
    on<AddRowEvent>((event, emit) {
      final newRow = HeaderRow(
        keyController: TextEditingController(),
        valueController: TextEditingController(),
        descriptionController: TextEditingController(),
      );
      final newRows = List<HeaderRow>.from(state.rows)..add(newRow);
      emit(state.copyWith(rows: newRows));
    });

    on<UpdateRowEvent>((event, emit) {
      final updatedRows = List<HeaderRow>.from(state.rows);
      final row = updatedRows[event.rowIndex];


      if (!row.isStatic || event.key == row.key) {
        updatedRows[event.rowIndex] = row.copyWith(
          key: event.key,
          value: event.value,
          description: event.description,
        );
      }

      emit(state.copyWith(rows: updatedRows));

      if (event.rowIndex == updatedRows.length - 1) {
        add(AddRowEvent());
      }
    });

    on<ToggleRowSelectionEvent>((event, emit) {
      final updatedRows = List<HeaderRow>.from(state.rows);
      updatedRows[event.rowIndex] =
          updatedRows[event.rowIndex].copyWith(isSelected: event.isSelected);
      emit(state.copyWith(rows: updatedRows));
    });

    on<LoadHeadersEvent>((event, emit) {
      final newRows = event.headers.entries.map((entry) {
        return HeaderRow(
          keyController: TextEditingController(text: entry.key),
          valueController: TextEditingController(text: entry.value),
          descriptionController: TextEditingController(),
          key: entry.key,
          value: entry.value,
          isSelected: true,
        );
      }).toList();

      emit(state.copyWith(rows: newRows));
    });

    on<ResetHeadersEvent>((event, emit) {
      emit(HeadersState(rows: [])); // Reset headers state
    });

  }
}

class HeadersBlocSingleton {
  static final HeadersBloc _headersBloc = HeadersBloc();

  static HeadersBloc get headersBloc => _headersBloc;

  // Private constructor to prevent instantiation
  HeadersBlocSingleton._();

  // Method to initialize the HeadersBloc with an event
  static void initialize() {
    _headersBloc.add(AddRowEvent()); // Add initialization event
  }
}






//working code
/*class HeadersBloc extends Bloc<HeadersEvent, HeadersState> {
  HeadersBloc() : super(const HeadersState(rows: [])) {
    on<AddRowEvent>((event, emit) {
      final newRow = HeaderRow(
        keyController: TextEditingController(),
        valueController: TextEditingController(),
        descriptionController: TextEditingController(),
      );
      final newRows = List<HeaderRow>.from(state.rows)..add(newRow);
      emit(state.copyWith(rows: newRows));
    });

    on<UpdateRowEvent>((event, emit) {
      final updatedRows = List<HeaderRow>.from(state.rows);
      final row = updatedRows[event.rowIndex];

      updatedRows[event.rowIndex] = row.copyWith(
        key: event.key,
        value: event.value,
        description: event.description,
      );

      emit(state.copyWith(rows: updatedRows));

      if (event.rowIndex == updatedRows.length - 1) {
        add(AddRowEvent());
      }
    });

    on<ToggleRowSelectionEvent>((event, emit) {
      final updatedRows = List<HeaderRow>.from(state.rows);
      updatedRows[event.rowIndex] =
          updatedRows[event.rowIndex].copyWith(isSelected: event.isSelected);
      emit(state.copyWith(rows: updatedRows));
    });
  }
}*/



