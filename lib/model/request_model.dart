
import 'dart:convert';

class RequestModel {
  final int? id;
  final String? name;
  final String? url;
  final String? method;
  final Map<String, dynamic>? headers;
  final Map<String, dynamic>? body;
  final Map<String, dynamic>? params;
  final int? collectionId;
  final int? folderId;
  final int? order;
  final int? isActive;
  final int? isDelete;

  RequestModel({
    this.id,
    this.name,
    this.url,
    this.method,
    this.headers,
    this.body,
    this.params,
    this.collectionId,
    this.folderId,
    this.order,
    this.isActive,
    this.isDelete,
  });

  factory RequestModel.fromJson(Map<String, dynamic> json) => RequestModel(
    id: json["id"],
    name: json["name"],
    url: json["url"],
    method: json["method"],
    headers: json["headers"] is Map ? Map<String, dynamic>.from(json["headers"]) : {},
    body: json["body"] is Map ? Map<String, dynamic>.from(json["body"]) : {},
    params: json["params"] is Map ? Map<String, dynamic>.from(json["params"]) : {},
    collectionId: json["collection_id"],
    folderId: json["folder_id"],
    order: json["order"],
    isActive: json["is_active"],
    isDelete: json["is_delete"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "url": url,
    "method": method,
    "headers": headers,
    "body": body,
    "params": params,
    "collection_id": collectionId,
    "folder_id": folderId,
    "order": order,
    "is_active": isActive,
    "is_delete": isDelete,
  };
}

